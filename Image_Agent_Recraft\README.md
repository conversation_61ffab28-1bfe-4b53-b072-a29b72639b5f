# Recraft Image Generation Agent

A professional AI-powered image generation agent specializing in vector graphics and scalable designs using the Recraft API. Built with LangChain, Flask, and modern vector processing libraries.

![Recraft Agent](https://img.shields.io/badge/AI-Powered-blue) ![Vector Graphics](https://img.shields.io/badge/Vector-Graphics-green) ![<PERSON><PERSON><PERSON><PERSON>](https://img.shields.io/badge/LangChain-Enabled-yellow)

## 🌟 Features

### Vector Graphics Specialization
- **Scalable SVG Generation**: Create professional vector graphics that scale perfectly at any size
- **PDF Output**: Print-ready vector documents for professional use
- **Brand Consistency**: Maintain consistent design elements across all outputs
- **Professional Styles**: Multiple artistic styles optimized for business and design use

### Advanced Image Processing
- **Background Removal**: AI-powered background detection and removal
- **Image Enhancement**: Quality improvement, sharpening, and color correction
- **Format Conversion**: Convert between PNG, JPG, SVG, PDF formats
- **Upscaling**: Increase image resolution while preserving quality
- **Vectorization**: Convert raster images to scalable vector formats

### User Interfaces
- **Web Interface**: Modern Flask-based web application with responsive design
- **CLI Interface**: Command-line tool for batch processing and automation
- **API Endpoints**: RESTful API for integration with other applications
- **Interactive Chat**: Conversational interface for natural image generation

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Recraft API key ([Get one here](https://recraft.ai))
- OpenAI API key (for LLM integration)

### Installation

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd Image_Agent_Recraft
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   # Windows
   venv\Scripts\activate
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment**
   ```bash
   cp .env.template .env
   # Edit .env file with your API keys
   ```

5. **Test Installation**
   ```bash
   python test_system.py
   ```

### Running the Application

#### Web Interface
```bash
python web_app.py
```
Then open http://localhost:5000 in your browser.

#### CLI Interface
```bash
# Interactive mode
python main.py

# Single command
python main.py "generate a modern company logo with blue colors"

# System tests
python main.py test
```

#### Docker Deployment
```bash
# Build and run
docker-compose up --build

# Production deployment with Nginx
docker-compose --profile production up
```

## 📁 Project Structure

```
Image_Agent_Recraft/
├── config/
│   ├── __init__.py
│   └── settings.py          # Configuration management
├── src/
│   ├── __init__.py
│   ├── agent.py             # Main Recraft agent
│   ├── recraft_tool.py      # Recraft API client
│   ├── recraft_langchain_tools.py  # LangChain tool wrappers
│   └── vector_processor.py  # Vector graphics processing
├── templates/               # Flask web templates
│   ├── base.html
│   ├── index.html
│   ├── vector.html
│   ├── processing.html
│   ├── gallery.html
│   └── error.html
├── static/                  # Static web assets
├── generated_images/        # Output directory
├── uploads/                 # Upload directory
├── main.py                  # CLI application
├── web_app.py              # Flask web application
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container configuration
├── docker-compose.yml      # Multi-container setup
├── .env.template           # Environment template
├── .gitignore              # Git ignore rules
└── README.md               # This file
```

## 🎨 Usage Examples

### Generate Vector Logo
```python
# CLI
python main.py "create a modern tech company logo with clean typography"

# Web interface
# Navigate to Vector Graphics page and enter your description
```

### Process Existing Image
```python
# CLI
python main.py "process remove_background from uploaded_image.png"

# Web interface
# Upload image on Processing page and select operation
```

### Chat Interface
```python
# Interactive chat
python main.py
# Then type: "I need a set of icons for my mobile app"
```

### API Usage
```bash
# Generate image via API
curl -X POST http://localhost:5000/api/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "modern logo design", "options": {"vector_output": true}}'
```

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Required API Keys
RECRAFT_API_KEY=your_recraft_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Optional Configuration
FLASK_ENV=development
FLASK_DEBUG=true
PORT=5000
SECRET_KEY=your-secret-key-here

# Advanced Settings
RECRAFT_API_BASE=https://external.api.recraft.ai/v1
DEFAULT_STYLE=digital_illustration
MAX_RETRIES=3
TIMEOUT=30
```

### Recraft Styles
The agent supports all Recraft styles:
- `realistic_image` - Photorealistic images
- `digital_illustration` - Digital art and illustrations
- `vector_illustration` - Clean vector graphics
- `icon` - Simple, scalable icons

### Vector Output Formats
- **SVG**: Scalable vector graphics for web and print
- **PDF**: Print-ready vector documents
- **PNG**: High-quality raster fallback

## 🔧 Advanced Usage

### Custom Vector Processing
```python
from src.vector_processor import VectorProcessor

processor = VectorProcessor()
# Convert PNG to SVG
processor.raster_to_vector('input.png', 'output.svg')
# Optimize SVG
processor.optimize_svg('input.svg', 'optimized.svg')
```

### Batch Processing
```python
# Process multiple images
python main.py batch_process --input-dir ./images --operation enhance
```

### API Integration
```python
import requests

# Generate with specific parameters
response = requests.post('http://localhost:5000/api/generate', json={
    'prompt': 'professional business card design',
    'options': {
        'vector_output': True,
        'style': 'vector_illustration',
        'size': '1024x1024'
    }
})
```

## 🛠️ Development

### Running Tests
```bash
# System tests
python test_system.py

# Unit tests
pytest tests/

# Linting
flake8 src/
black src/
```

### Adding New Features
1. Create feature branch: `git checkout -b feature/new-feature`
2. Add functionality in appropriate module
3. Update tests and documentation
4. Submit pull request

### Debugging
```bash
# Enable debug mode
export FLASK_DEBUG=true
python web_app.py

# Verbose logging
export LOG_LEVEL=DEBUG
python main.py
```

## 🐳 Docker Deployment

### Development
```bash
docker-compose up --build
```

### Production
```bash
# With Nginx reverse proxy
docker-compose --profile production up -d

# Environment-specific override
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Health Monitoring
```bash
# Check container health
docker-compose ps
docker-compose logs recraft-agent

# Health endpoint
curl http://localhost:5000/health
```

## 📊 Monitoring and Logs

### Log Files
- Application logs: `logs/app.log`
- Error logs: `logs/error.log`
- Access logs: `logs/access.log`

### Health Checks
- `/health` - System health status
- `/api/status` - Agent readiness status
- Docker healthcheck included

### Performance Monitoring
```bash
# Check resource usage
docker stats recraft-image-agent

# Monitor response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5000/api/status
```

## 🔒 Security

### API Key Security
- Store API keys in environment variables only
- Never commit keys to version control
- Use different keys for development/production
- Rotate keys regularly

### Web Security
- CORS protection enabled
- File upload restrictions
- Input validation and sanitization
- Rate limiting (in production setup)

### Docker Security
- Non-root user in container
- Minimal base image
- Security scanning enabled
- Read-only filesystem where possible

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Contribution Guidelines
- Follow PEP 8 coding standards
- Add tests for new features
- Update documentation
- Ensure all tests pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Recraft AI](https://recraft.ai) for the powerful vector graphics API
- [LangChain](https://langchain.com) for the AI agent framework
- [Flask](https://flask.palletsprojects.com) for the web framework
- Vector graphics libraries: CairoSVG, ReportLab, SVGLib

## 🆘 Support

### Common Issues

**Q: "Import errors when running the application"**
A: Ensure all dependencies are installed: `pip install -r requirements.txt`

**Q: "API key not found error"**
A: Check your `.env` file has the correct API keys set.

**Q: "Vector processing fails"**
A: Ensure system dependencies are installed (Cairo, librsvg, potrace).

**Q: "Web interface not accessible"**
A: Check if Flask is running on the correct port (default 5000).

### Getting Help
- Create an issue for bugs or feature requests
- Check existing issues for similar problems
- Include error logs and system information
- Provide minimal reproducible example

### Contact
- Email: [<EMAIL>]
- GitHub: [your-github-profile]
- Documentation: [project-docs-url]

---

<div align="center">
  <strong>Built with ❤️ for professional vector graphics and AI-powered design</strong>
</div>