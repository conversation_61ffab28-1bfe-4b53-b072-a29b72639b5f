{% extends "base.html" %}

{% block title %}Error - <PERSON><PERSON> Vector Graphics Agent{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card text-center">
            <div class="card-body py-5">
                <div class="mb-4">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                </div>
                
                <h1 class="display-4 text-danger mb-3">
                    {% if error_code %}Error {{ error_code }}{% else %}Oops!{% endif %}
                </h1>
                
                <p class="lead text-muted mb-4">
                    {% if error %}
                        {{ error }}
                    {% else %}
                        Something went wrong. Please try again later.
                    {% endif %}
                </p>
                
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="/" class="btn btn-recraft">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                    <button class="btn btn-outline-recraft" onclick="history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </button>
                </div>
                
                {% if error_code == 404 %}
                <div class="mt-4">
                    <h5>Suggestions:</h5>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-decoration-none">Home - Start generating images</a></li>
                        <li><a href="/vector" class="text-decoration-none">Vector Graphics - Create scalable designs</a></li>
                        <li><a href="/processing" class="text-decoration-none">Image Processing - Enhance your images</a></li>
                        <li><a href="/gallery" class="text-decoration-none">Gallery - View generated images</a></li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}