"""
Base Flask application framework for Image Agent providers.
Provides common web interface patterns and utilities.
"""
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash, send_from_directory, abort
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
import os
import json
import logging
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Callable
import secrets
from functools import wraps

from ..core.src.file_manager import FileManager, TempFileManager
from ..core.src.validation import InputValidator, SecurityValidator, ContentFilter
from ..core.src.image_encoder import ImageEncoder

logger = logging.getLogger(__name__)


class BaseFlaskApp:
    """Base Flask application for Image Agents."""
    
    def __init__(self, name: str, provider_name: str, template_folder: str = None, static_folder: str = None):
        """
        Initialize Flask application.
        
        Args:
            name: Application name
            provider_name: AI provider name
            template_folder: Templates directory
            static_folder: Static files directory
        """
        self.provider_name = provider_name
        self.app = Flask(
            name, 
            template_folder=template_folder or 'templates',
            static_folder=static_folder or 'static'
        )
        
        # Initialize utilities
        self.file_manager = FileManager()
        self.temp_manager = TempFileManager()
        self.validator = InputValidator()
        self.security_validator = SecurityValidator()
        self.content_filter = ContentFilter()
        self.image_encoder = ImageEncoder()
        
        # Configuration
        self.configure_app()
        
        # Register routes
        self.register_common_routes()
        
        # Register error handlers
        self.register_error_handlers()
        
        # Set up session cleanup
        self.setup_session_cleanup()
    
    def configure_app(self):
        """Configure Flask application settings."""
        # Secret key for sessions
        self.app.secret_key = os.getenv('SECRET_KEY', secrets.token_urlsafe(32))
        
        # File upload settings
        self.app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_UPLOAD_SIZE', 16 * 1024 * 1024))  # 16MB
        self.app.config['UPLOAD_EXTENSIONS'] = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        
        # Session settings
        self.app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
        
        # Security settings
        self.app.config['SESSION_COOKIE_SECURE'] = os.getenv('FLASK_ENV') == 'production'
        self.app.config['SESSION_COOKIE_HTTPONLY'] = True
        self.app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
        
        # Provider-specific settings
        self.app.config['PROVIDER_NAME'] = self.provider_name
        self.app.config['DEBUG'] = os.getenv('DEBUG', 'false').lower() == 'true'
        
    def register_common_routes(self):
        """Register common routes used by all providers."""
        
        @self.app.route('/')
        def index():
            """Main page route."""
            return render_template('index.html', 
                                 provider_name=self.provider_name,
                                 session_id=session.get('session_id'))
        
        @self.app.route('/health')
        def health_check():
            """Health check endpoint."""
            return jsonify({
                'status': 'healthy',
                'provider': self.provider_name,
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/session', methods=['POST'])
        def create_session():
            """Create new session."""
            session_id = secrets.token_urlsafe(16)
            session['session_id'] = session_id
            session['created_at'] = datetime.now().isoformat()
            session['provider'] = self.provider_name
            session.permanent = True
            
            logger.info(f"Created session: {session_id}")
            return jsonify({'session_id': session_id})
        
        @self.app.route('/api/session', methods=['GET'])
        def get_session():
            """Get current session info."""
            if 'session_id' not in session:
                return jsonify({'error': 'No active session'}), 404
            
            return jsonify({
                'session_id': session['session_id'],
                'provider': session.get('provider'),
                'created_at': session.get('created_at')
            })
        
        @self.app.route('/api/upload', methods=['POST'])
        def upload_file():
            """Handle file uploads."""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'No file provided'}), 400
                
                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'No file selected'}), 400
                
                # Validate file
                if not self._is_allowed_file(file.filename):
                    return jsonify({'error': 'Invalid file type'}), 400
                
                # Security scan
                file_content = file.read()
                file.seek(0)  # Reset file pointer
                
                if not self.security_validator.is_safe_upload(file_content, file.filename):
                    return jsonify({'error': 'File failed security validation'}), 400
                
                # Save file
                filename = secure_filename(file.filename)
                session_id = session.get('session_id', 'anonymous')
                
                saved_path = self.temp_manager.save_upload(
                    file_content, 
                    filename,
                    session_id
                )
                
                # Encode for preview if it's an image
                file_info = {
                    'filename': filename,
                    'path': str(saved_path),
                    'size': len(file_content)
                }
                
                if self._is_image_file(filename):
                    try:
                        file_info['preview'] = self.image_encoder.encode_image_to_base64(saved_path)
                        file_info['dimensions'] = self.image_encoder.get_image_info(saved_path)
                    except Exception as e:
                        logger.warning(f"Failed to process image for preview: {e}")
                
                logger.info(f"File uploaded successfully: {filename}")
                return jsonify(file_info)
                
            except RequestEntityTooLarge:
                return jsonify({'error': 'File too large'}), 413
            except Exception as e:
                logger.error(f"Upload error: {e}")
                return jsonify({'error': 'Upload failed'}), 500
        
        @self.app.route('/api/validate', methods=['POST'])
        def validate_input():
            """Validate user input."""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400
                
                results = {}
                
                # Validate prompt
                if 'prompt' in data:
                    try:
                        self.validator.validate_prompt(data['prompt'])
                        self.content_filter.check_content_appropriateness(data['prompt'])
                        results['prompt'] = {'valid': True}
                    except ValueError as e:
                        results['prompt'] = {'valid': False, 'error': str(e)}
                
                # Validate dimensions
                if 'width' in data and 'height' in data:
                    try:
                        self.validator.validate_image_size(data['width'], data['height'])
                        results['dimensions'] = {'valid': True}
                    except ValueError as e:
                        results['dimensions'] = {'valid': False, 'error': str(e)}
                
                # Overall validation
                all_valid = all(result.get('valid', False) for result in results.values())
                
                return jsonify({
                    'valid': all_valid,
                    'results': results
                })
                
            except Exception as e:
                logger.error(f"Validation error: {e}")
                return jsonify({'error': 'Validation failed'}), 500
        
        @self.app.route('/download/<path:filename>')
        def download_file(filename):
            """Download generated files."""
            try:
                # Security: validate filename and session
                if not self.security_validator.is_safe_filename(filename):
                    abort(403)
                
                session_id = session.get('session_id')
                if not session_id:
                    abort(401)
                
                # Check if file belongs to session
                file_path = self.file_manager.output_dir / filename
                if not file_path.exists():
                    abort(404)
                
                return send_from_directory(
                    self.file_manager.output_dir,
                    filename,
                    as_attachment=True
                )
                
            except Exception as e:
                logger.error(f"Download error: {e}")
                abort(500)
        
        @self.app.route('/api/history')
        def get_history():
            """Get generation history for session."""
            try:
                session_id = session.get('session_id')
                if not session_id:
                    return jsonify([])
                
                # This would be implemented by each provider
                # to return their specific history format
                return jsonify(self.get_session_history(session_id))
                
            except Exception as e:
                logger.error(f"History error: {e}")
                return jsonify({'error': 'Failed to get history'}), 500
        
        @self.app.route('/api/clear-session', methods=['POST'])
        def clear_session():
            """Clear current session and temporary files."""
            try:
                session_id = session.get('session_id')
                if session_id:
                    # Clean up temporary files
                    self.temp_manager.cleanup_session(session_id)
                    logger.info(f"Cleaned up session: {session_id}")
                
                session.clear()
                return jsonify({'success': True})
                
            except Exception as e:
                logger.error(f"Session clear error: {e}")
                return jsonify({'error': 'Failed to clear session'}), 500
    
    def register_error_handlers(self):
        """Register common error handlers."""
        
        @self.app.errorhandler(404)
        def not_found(error):
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Not found'}), 404
            return render_template('error.html', 
                                 error_code=404, 
                                 error_message="Page not found"), 404
        
        @self.app.errorhandler(413)
        def file_too_large(error):
            if request.path.startswith('/api/'):
                return jsonify({'error': 'File too large'}), 413
            return render_template('error.html',
                                 error_code=413,
                                 error_message="File too large"), 413
        
        @self.app.errorhandler(500)
        def internal_error(error):
            logger.error(f"Internal error: {error}")
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Internal server error'}), 500
            return render_template('error.html',
                                 error_code=500,
                                 error_message="Internal server error"), 500
        
        @self.app.errorhandler(Exception)
        def handle_exception(e):
            logger.error(f"Unhandled exception: {e}\n{traceback.format_exc()}")
            if request.path.startswith('/api/'):
                return jsonify({'error': 'An unexpected error occurred'}), 500
            return render_template('error.html',
                                 error_code=500,
                                 error_message="An unexpected error occurred"), 500
    
    def setup_session_cleanup(self):
        """Set up automatic session cleanup."""
        
        @self.app.before_request
        def cleanup_expired_sessions():
            """Clean up expired sessions before each request."""
            try:
                # Only run cleanup occasionally to avoid performance impact
                if request.endpoint == 'health_check':
                    self.temp_manager.cleanup_expired_files()
            except Exception as e:
                logger.warning(f"Session cleanup error: {e}")
    
    def _is_allowed_file(self, filename: str) -> bool:
        """Check if file extension is allowed."""
        if not filename:
            return False
        
        extension = Path(filename).suffix.lower()
        return extension in self.app.config['UPLOAD_EXTENSIONS']
    
    def _is_image_file(self, filename: str) -> bool:
        """Check if file is an image."""
        if not filename:
            return False
        
        extension = Path(filename).suffix.lower()
        return extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    
    def get_session_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get generation history for a session.
        Override this method in provider-specific implementations.
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of generation history items
        """
        return []
    
    def require_session(self, f: Callable) -> Callable:
        """Decorator to require active session."""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'session_id' not in session:
                if request.path.startswith('/api/'):
                    return jsonify({'error': 'Session required'}), 401
                else:
                    flash('Please start a new session')
                    return redirect(url_for('index'))
            return f(*args, **kwargs)
        return decorated_function
    
    def add_provider_routes(self, routes_blueprint) -> None:
        """
        Add provider-specific routes.
        
        Args:
            routes_blueprint: Flask blueprint with provider routes
        """
        self.app.register_blueprint(routes_blueprint)
    
    def add_template_globals(self, globals_dict: Dict[str, Any]) -> None:
        """
        Add global variables to template context.
        
        Args:
            globals_dict: Dictionary of global variables
        """
        for key, value in globals_dict.items():
            self.app.jinja_env.globals[key] = value
    
    def add_template_filters(self, filters_dict: Dict[str, Callable]) -> None:
        """
        Add custom template filters.
        
        Args:
            filters_dict: Dictionary of filter functions
        """
        for name, filter_func in filters_dict.items():
            self.app.jinja_env.filters[name] = filter_func
    
    def run(self, host: str = '0.0.0.0', port: int = 5000, debug: bool = None) -> None:
        """
        Run the Flask application.
        
        Args:
            host: Host address
            port: Port number
            debug: Debug mode
        """
        if debug is None:
            debug = self.app.config.get('DEBUG', False)
        
        logger.info(f"Starting {self.provider_name} Image Agent on {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


class APIRoutes:
    """Helper class for API route management."""
    
    @staticmethod
    def create_generation_route(generator_func: Callable) -> Callable:
        """
        Create a standardized generation API route.
        
        Args:
            generator_func: Function that generates images
            
        Returns:
            Flask route function
        """
        def generate():
            try:
                if 'session_id' not in session:
                    return jsonify({'error': 'Session required'}), 401
                
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400
                
                # Call the provider-specific generator
                result = generator_func(data, session['session_id'])
                
                return jsonify(result)
                
            except ValueError as e:
                logger.warning(f"Validation error: {e}")
                return jsonify({'error': str(e)}), 400
            except Exception as e:
                logger.error(f"Generation error: {e}")
                return jsonify({'error': 'Generation failed'}), 500
        
        return generate
    
    @staticmethod
    def create_status_route(status_func: Callable) -> Callable:
        """
        Create a standardized status API route.
        
        Args:
            status_func: Function that checks generation status
            
        Returns:
            Flask route function
        """
        def get_status(task_id: str):
            try:
                if 'session_id' not in session:
                    return jsonify({'error': 'Session required'}), 401
                
                status = status_func(task_id, session['session_id'])
                return jsonify(status)
                
            except Exception as e:
                logger.error(f"Status check error: {e}")
                return jsonify({'error': 'Status check failed'}), 500
        
        return get_status


class WebSocketManager:
    """Manager for WebSocket connections (if needed)."""
    
    def __init__(self):
        """Initialize WebSocket manager."""
        self.connections = {}
        self.session_connections = {}
    
    def add_connection(self, session_id: str, websocket):
        """Add WebSocket connection for session."""
        if session_id not in self.session_connections:
            self.session_connections[session_id] = []
        
        self.session_connections[session_id].append(websocket)
        logger.info(f"Added WebSocket connection for session: {session_id}")
    
    def remove_connection(self, session_id: str, websocket):
        """Remove WebSocket connection."""
        if session_id in self.session_connections:
            if websocket in self.session_connections[session_id]:
                self.session_connections[session_id].remove(websocket)
            
            if not self.session_connections[session_id]:
                del self.session_connections[session_id]
        
        logger.info(f"Removed WebSocket connection for session: {session_id}")
    
    def send_to_session(self, session_id: str, message: Dict[str, Any]):
        """Send message to all connections for a session."""
        if session_id in self.session_connections:
            for websocket in self.session_connections[session_id]:
                try:
                    websocket.send(json.dumps(message))
                except Exception as e:
                    logger.warning(f"Failed to send WebSocket message: {e}")
                    # Connection might be dead, remove it
                    self.remove_connection(session_id, websocket)