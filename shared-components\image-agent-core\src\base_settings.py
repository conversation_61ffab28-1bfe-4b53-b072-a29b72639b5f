"""
Base settings framework for all Image Agent providers.
Provides common configuration patterns and utilities.
"""
import os
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dotenv import load_dotenv
import json

logger = logging.getLogger(__name__)


class BaseSettings:
    """Base configuration class for Image Agents."""
    
    def __init__(self, env_file: str = '.env', config_file: str = None):
        """
        Initialize base settings.
        
        Args:
            env_file: Path to environment file
            config_file: Path to JSON config file
        """
        self.env_file = env_file
        self.config_file = config_file
        self._config = {}
        
        # Load environment variables
        load_dotenv(env_file)
        
        # Load config file if provided
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    self._config = json.load(f)
                logger.debug(f"Loaded config from {config_file}")
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
    
    def get_env_var(self, key: str, default: Any = None, required: bool = False) -> Any:
        """
        Get environment variable with validation.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            required: Whether this variable is required
            
        Returns:
            Environment variable value or default
            
        Raises:
            ValueError: If required variable is missing
        """
        value = os.getenv(key, default)
        
        if required and value is None:
            raise ValueError(f"Required environment variable '{key}' is not set")
        
        return value
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get value from config file with dot notation support.
        
        Args:
            key: Config key (supports 'section.key' notation)
            default: Default value if not found
            
        Returns:
            Config value or default
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def validate_api_key(self, api_key: str, provider: str = "API") -> bool:
        """
        Validate API key format.
        
        Args:
            api_key: API key to validate
            provider: Provider name for error messages
            
        Returns:
            True if valid
            
        Raises:
            ValueError: If API key is invalid
        """
        if not api_key:
            raise ValueError(f"{provider} API key is required")
        
        if not isinstance(api_key, str):
            raise ValueError(f"{provider} API key must be a string")
        
        if len(api_key) < 10:
            raise ValueError(f"{provider} API key is too short")
        
        if len(api_key) > 200:
            raise ValueError(f"{provider} API key is too long")
        
        return True
    
    def validate_directory(self, path: str, create: bool = True) -> str:
        """
        Validate and optionally create directory.
        
        Args:
            path: Directory path
            create: Whether to create directory if it doesn't exist
            
        Returns:
            Validated directory path
            
        Raises:
            ValueError: If directory is invalid
        """
        if not path:
            raise ValueError("Directory path cannot be empty")
        
        path_obj = Path(path)
        
        if path_obj.exists() and not path_obj.is_dir():
            raise ValueError(f"Path exists but is not a directory: {path}")
        
        if create and not path_obj.exists():
            path_obj.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {path}")
        
        return str(path_obj.absolute())
    
    def get_directory_setting(self, env_key: str, config_key: str, default: str, create: bool = True) -> str:
        """
        Get directory setting from environment or config with validation.
        
        Args:
            env_key: Environment variable name
            config_key: Config file key
            default: Default directory path
            create: Whether to create directory
            
        Returns:
            Validated directory path
        """
        directory = self.get_env_var(env_key) or self.get_config_value(config_key, default)
        return self.validate_directory(directory, create)
    
    @classmethod
    def create_env_template(cls, template_path: str = '.env.template', 
                           variables: Dict[str, str] = None) -> str:
        """
        Create environment template file.
        
        Args:
            template_path: Path for template file
            variables: Dictionary of variable descriptions
            
        Returns:
            Path to created template
        """
        if variables is None:
            variables = {
                'API_KEY': 'Your API key here',
                'OPENAI_API_KEY': 'Your OpenAI API key for LLM integration',
                'DEBUG': 'true/false - Enable debug mode',
                'LOG_LEVEL': 'DEBUG/INFO/WARNING/ERROR - Logging level',
                'MAX_RETRIES': '3 - Maximum API retry attempts',
                'TIMEOUT': '30 - Request timeout in seconds',
            }
        
        template_content = []
        template_content.append("# Image Agent Environment Configuration")
        template_content.append("# Copy this file to .env and fill in your values")
        template_content.append("")
        
        for key, description in variables.items():
            template_content.append(f"# {description}")
            template_content.append(f"{key}=")
            template_content.append("")
        
        template_content.append("# Optional Settings")
        template_content.append("# FLASK_ENV=development")
        template_content.append("# PORT=5000")
        template_content.append("# SECRET_KEY=your-secret-key-here")
        
        with open(template_path, 'w') as f:
            f.write('\n'.join(template_content))
        
        logger.info(f"Created environment template: {template_path}")
        return template_path


class CommonPaths:
    """Common path utilities for Image Agents."""
    
    def __init__(self, base_dir: str = None):
        """
        Initialize path manager.
        
        Args:
            base_dir: Base directory for the agent
        """
        self.base_dir = Path(base_dir) if base_dir else Path.cwd()
        
    @property
    def config_dir(self) -> Path:
        """Get configuration directory."""
        return self.base_dir / 'config'
    
    @property
    def logs_dir(self) -> Path:
        """Get logs directory."""
        return self.base_dir / 'logs'
    
    @property
    def output_dir(self) -> Path:
        """Get output directory for generated images."""
        return self.base_dir / 'generated_images'
    
    @property
    def uploads_dir(self) -> Path:
        """Get uploads directory."""
        return self.base_dir / 'uploads'
    
    @property
    def temp_dir(self) -> Path:
        """Get temporary files directory."""
        return self.base_dir / 'temp'
    
    @property
    def static_dir(self) -> Path:
        """Get static files directory for web interface."""
        return self.base_dir / 'static'
    
    @property
    def templates_dir(self) -> Path:
        """Get templates directory for web interface."""
        return self.base_dir / 'templates'
    
    def ensure_all_directories(self):
        """Ensure all common directories exist."""
        directories = [
            self.config_dir,
            self.logs_dir,
            self.output_dir,
            self.uploads_dir,
            self.temp_dir,
            self.static_dir,
            self.templates_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        logger.debug("Ensured all common directories exist")


class LoggingConfig:
    """Logging configuration utilities."""
    
    @staticmethod
    def setup_logging(log_level: str = 'INFO', 
                     log_file: str = None,
                     format_string: str = None) -> None:
        """
        Set up logging configuration.
        
        Args:
            log_level: Logging level
            log_file: Optional log file path
            format_string: Custom format string
        """
        if format_string is None:
            format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Convert string level to logging constant
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Configure root logger
        logging.basicConfig(
            level=numeric_level,
            format=format_string,
            handlers=[]
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_formatter = logging.Formatter(format_string)
        console_handler.setFormatter(console_formatter)
        
        # File handler if specified
        handlers = [console_handler]
        if log_file:
            # Ensure log directory exists
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(numeric_level)
            file_formatter = logging.Formatter(format_string)
            file_handler.setFormatter(file_formatter)
            handlers.append(file_handler)
        
        # Configure root logger with handlers
        root_logger = logging.getLogger()
        root_logger.handlers = handlers
        
        logger.info(f"Logging configured: level={log_level}, file={log_file}")


class ProviderConfig:
    """Base configuration for AI providers."""
    
    def __init__(self, provider_name: str):
        """
        Initialize provider configuration.
        
        Args:
            provider_name: Name of the AI provider
        """
        self.provider_name = provider_name
        self.api_key = None
        self.api_base = None
        self.api_version = None
        self.timeout = 30
        self.max_retries = 3
        self.rate_limit = None
        
    def load_from_env(self, key_prefix: str = None) -> None:
        """
        Load configuration from environment variables.
        
        Args:
            key_prefix: Prefix for environment variable names
        """
        if key_prefix is None:
            key_prefix = self.provider_name.upper()
        
        self.api_key = os.getenv(f'{key_prefix}_API_KEY')
        self.api_base = os.getenv(f'{key_prefix}_API_BASE')
        self.api_version = os.getenv(f'{key_prefix}_API_VERSION')
        self.timeout = int(os.getenv(f'{key_prefix}_TIMEOUT', self.timeout))
        self.max_retries = int(os.getenv(f'{key_prefix}_MAX_RETRIES', self.max_retries))
        
        rate_limit_str = os.getenv(f'{key_prefix}_RATE_LIMIT')
        if rate_limit_str:
            self.rate_limit = int(rate_limit_str)
    
    def validate(self) -> bool:
        """
        Validate the configuration.
        
        Returns:
            True if configuration is valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        if not self.api_key:
            raise ValueError(f"{self.provider_name} API key is required")
        
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        
        if self.max_retries < 0:
            raise ValueError("Max retries cannot be negative")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Configuration dictionary
        """
        return {
            'provider_name': self.provider_name,
            'api_key': '***' if self.api_key else None,  # Masked for security
            'api_base': self.api_base,
            'api_version': self.api_version,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'rate_limit': self.rate_limit,
        }


class DatabaseConfig:
    """Database configuration utilities."""
    
    @staticmethod
    def get_database_url(provider: str = 'sqlite', **kwargs) -> str:
        """
        Generate database URL for different providers.
        
        Args:
            provider: Database provider (sqlite, postgresql, mysql)
            **kwargs: Connection parameters
            
        Returns:
            Database URL string
        """
        if provider == 'sqlite':
            db_path = kwargs.get('path', 'image_agent.db')
            return f"sqlite:///{db_path}"
        
        elif provider == 'postgresql':
            user = kwargs.get('user', 'postgres')
            password = kwargs.get('password', '')
            host = kwargs.get('host', 'localhost')
            port = kwargs.get('port', 5432)
            database = kwargs.get('database', 'image_agent')
            
            return f"postgresql://{user}:{password}@{host}:{port}/{database}"
        
        elif provider == 'mysql':
            user = kwargs.get('user', 'root')
            password = kwargs.get('password', '')
            host = kwargs.get('host', 'localhost')
            port = kwargs.get('port', 3306)
            database = kwargs.get('database', 'image_agent')
            
            return f"mysql://{user}:{password}@{host}:{port}/{database}"
        
        else:
            raise ValueError(f"Unsupported database provider: {provider}")


class FeatureFlags:
    """Feature flag management."""
    
    def __init__(self, flags: Dict[str, bool] = None):
        """
        Initialize feature flags.
        
        Args:
            flags: Dictionary of feature flags
        """
        self.flags = flags or {}
        
        # Load from environment
        for key, value in os.environ.items():
            if key.startswith('FEATURE_'):
                flag_name = key[8:].lower()  # Remove 'FEATURE_' prefix
                self.flags[flag_name] = value.lower() in ('true', '1', 'yes', 'on')
    
    def is_enabled(self, feature: str, default: bool = False) -> bool:
        """
        Check if feature is enabled.
        
        Args:
            feature: Feature name
            default: Default value if not set
            
        Returns:
            True if feature is enabled
        """
        return self.flags.get(feature.lower(), default)
    
    def enable(self, feature: str) -> None:
        """Enable a feature."""
        self.flags[feature.lower()] = True
    
    def disable(self, feature: str) -> None:
        """Disable a feature."""
        self.flags[feature.lower()] = False
    
    def toggle(self, feature: str) -> bool:
        """
        Toggle a feature flag.
        
        Args:
            feature: Feature name
            
        Returns:
            New state of the feature
        """
        current = self.is_enabled(feature)
        self.flags[feature.lower()] = not current
        return not current