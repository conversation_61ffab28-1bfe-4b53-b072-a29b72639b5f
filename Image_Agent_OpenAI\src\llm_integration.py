"""
Language Model Integration for the Langchain Image Generation Agent.
"""
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from config.settings import Settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMIntegration:
    """Handles the integration with OpenAI's language model."""
    
    def __init__(self):
        """Initialize the LLM integration."""
        self.llm = ChatOpenAI(
            model=Settings.LANGUAGE_MODEL,
            openai_api_key=Settings.OPENAI_API_KEY,
            temperature=0.7,
            max_tokens=1000
        )
        logger.info(f"Initialized LLM with model: {Settings.LANGUAGE_MODEL}")
    
    def invoke(self, messages):
        """
        Invoke the language model with a list of messages.
        
        Args:
            messages: List of message objects (HumanMessage, AIMessage, SystemMessage)
            
        Returns:
            AIMessage: Response from the language model
        """
        try:
            response = self.llm.invoke(messages)
            logger.info("Successfully invoked LLM")
            return response
        except Exception as e:
            logger.error(f"Error invoking LLM: {str(e)}")
            raise
    
    def test_connectivity(self):
        """
        Test basic connectivity to the language model.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            test_message = [
                SystemMessage(content="You are a helpful AI assistant."),
                HumanMessage(content="Hello! Please respond with 'Connection successful'.")
            ]
            response = self.invoke(test_message)
            logger.info("LLM connectivity test passed")
            return True
        except Exception as e:
            logger.error(f"LLM connectivity test failed: {str(e)}")
            return False

# Test function for standalone usage
def test_llm_integration():
    """Test the LLM integration."""
    try:
        llm_integration = LLMIntegration()
        
        # Test basic connectivity
        if llm_integration.test_connectivity():
            print("✓ LLM integration test passed")
            return True
        else:
            print("✗ LLM integration test failed")
            return False
    except Exception as e:
        print(f"✗ LLM integration test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_llm_integration()