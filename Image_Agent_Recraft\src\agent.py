"""
Recraft Image Generation Agent.
Advanced AI agent specialized in vector graphics and professional design.
"""
from typing import List, Dict, Any, Optional
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_openai_tools_agent, AgentExecutor
from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from .recraft_langchain_tools import RecraftImageTool, RecraftProcessingTool, RecraftStyleTool
from .vector_processor import VectorProcessor
from config.settings import Settings
import logging
import os

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LLMIntegration:
    """LLM integration for the Recraft agent."""
    
    def __init__(self):
        """Initialize LLM integration."""
        # Use OpenAI for consistency with the original architecture
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required for LLM functionality")
        
        self.llm = ChatOpenAI(
            model=Settings.LANGUAGE_MODEL,
            temperature=0.7,
            api_key=openai_api_key
        )
        logger.info("LLM integration initialized successfully")
    
    def test_connectivity(self) -> bool:
        """Test LLM connectivity."""
        try:
            response = self.llm.invoke([HumanMessage(content="Hello, this is a connectivity test.")])
            return bool(response.content)
        except Exception as e:
            logger.error(f"LLM connectivity test failed: {e}")
            return False


class RecraftAgent:
    """
    Main Recraft agent class that combines LLM reasoning with 
    vector graphics generation and professional design capabilities.
    """
    
    def __init__(self):
        """Initialize the Recraft agent."""
        logger.info("Initializing Recraft Image Generation Agent...")
        
        try:
            # Initialize components
            self.llm_integration = LLMIntegration()
            self.vector_processor = VectorProcessor()
            
            # Initialize custom ruleset support
            self.custom_ruleset = None
            self.default_system_prompt = self._get_default_system_prompt()
            
            # Create the agent prompt
            self.prompt = self._create_agent_prompt()
            
            # Initialize tools
            self.recraft_image_tool = RecraftImageTool()
            self.recraft_processing_tool = RecraftProcessingTool()
            self.recraft_style_tool = RecraftStyleTool()
            
            # Create tools list
            self.tools = [
                self.recraft_image_tool,
                self.recraft_processing_tool,
                self.recraft_style_tool
            ]
            
            # Create the agent
            self.agent = create_openai_tools_agent(
                llm=self.llm_integration.llm,
                tools=self.tools,
                prompt=self.prompt
            )
            
            # Create agent executor
            self.agent_executor = AgentExecutor(
                agent=self.agent,
                tools=self.tools,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=3
            )
            
            logger.info("Recraft Image Generation Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Recraft agent: {str(e)}")
            raise
    
    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for the Recraft agent."""
        return """You are an advanced AI assistant specialized in vector graphics generation and professional design using Recraft's cutting-edge API technology.

🎨 **CORE CAPABILITIES:**
- **Vector Graphics Excellence**: Generate scalable SVG and PDF graphics perfect for logos, icons, and professional designs
- **Multi-Format Output**: Create both raster (PNG/JPG) and vector (SVG/PDF) versions simultaneously
- **Professional Design Styles**: Access to curated styles for realistic images, digital illustrations, and artistic creations
- **Advanced Image Processing**: Background removal/replacement, upscaling, inpainting, and vectorization
- **Brand-Consistent Graphics**: Maintain visual consistency across design projects

🔧 **AVAILABLE TOOLS:**
1. **RecraftImageTool** - Generate new images with optional vector output
2. **RecraftProcessingTool** - Process existing images (edit, enhance, convert)
3. **RecraftStyleTool** - Get style information and recommendations

📋 **INTELLIGENT TOOL SELECTION:**

**For NEW IMAGE GENERATION:**
✅ Use RecraftImageTool with detailed prompts
✅ Consider vector output for logos, icons, graphics
✅ Choose appropriate style and substyle combinations
✅ Specify size based on intended use

**For IMAGE PROCESSING:**
✅ Use RecraftProcessingTool for editing existing images
✅ Support operations: image_to_image, remove_background, replace_background, crisp_upscale, creative_upscale, inpaint, vectorize

**For STYLE GUIDANCE:**
✅ Use RecraftStyleTool to help users choose styles
✅ Recommend style combinations for specific use cases
✅ Explain style differences and applications

🎯 **VECTOR GRAPHICS SPECIALIZATION:**
- **Logos & Branding**: Use vector output for scalable brand assets
- **Icon Design**: Leverage style options for consistent icon families  
- **Professional Graphics**: Recommend appropriate styles for business use
- **Print Design**: Consider vector formats for high-quality printing

💡 **DESIGN BEST PRACTICES:**
- Ask about intended use (web, print, mobile, etc.)
- Suggest appropriate styles based on brand/project requirements
- Recommend vector output for scalable graphics
- Consider file size and format based on use case
- Provide style guidance for consistent visual identity

🔍 **USER INTERACTION APPROACH:**
1. **Understand the Request**: Clarify intended use and requirements
2. **Recommend Optimal Settings**: Suggest styles, sizes, and formats
3. **Generate with Intelligence**: Use appropriate tools and parameters
4. **Provide Guidance**: Explain choices and offer alternatives
5. **Deliver Professional Results**: Focus on quality and usability

⚠️ **CRITICAL GUIDELINES:**
- Always prioritize vector output for logos, icons, and scalable graphics
- Match style recommendations to professional use cases
- Provide clear explanations of tool choices and parameters
- Focus on practical, usable results for design workflows
- Maintain brand consistency when generating multiple assets

🎨 **STYLE SPECIALIZATIONS:**
- **Realistic Images**: Use 'realistic_image' style with appropriate substyles
- **Digital Illustrations**: Use 'digital_illustration' for clean, modern graphics
- **Vector Graphics**: Always offer vector output for scalable designs
- **Brand Assets**: Maintain consistency across related designs

Remember: You're not just generating images - you're creating professional design assets that users can confidently use in their projects, presentations, and brand materials.
"""
    
    def _create_agent_prompt(self) -> ChatPromptTemplate:
        """Create the agent prompt template."""
        system_prompt = self.custom_ruleset or self.default_system_prompt
        
        return ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
    
    def ensure_custom_ruleset(self, ruleset: str):
        """Ensure custom ruleset is applied."""
        if self.custom_ruleset != ruleset:
            self.custom_ruleset = ruleset
            # Recreate prompt with new ruleset
            self.prompt = self._create_agent_prompt()
            
            # Recreate agent with new prompt
            self.agent = create_openai_tools_agent(
                llm=self.llm_integration.llm,
                tools=self.tools,
                prompt=self.prompt
            )
            
            # Update executor
            self.agent_executor = AgentExecutor(
                agent=self.agent,
                tools=self.tools,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=3
            )
            
            logger.info("Custom ruleset applied successfully")
    
    def invoke(self, query: str, chat_history: Optional[List[BaseMessage]] = None) -> str:
        """
        Invoke the agent with a query.
        
        Args:
            query: User query/request
            chat_history: Optional chat history for context
            
        Returns:
            str: Agent response
        """
        try:
            logger.info(f"Processing query: {query[:100]}...")
            
            # Prepare input
            agent_input = {
                "input": query,
                "chat_history": chat_history or []
            }
            
            # Execute the agent
            result = self.agent_executor.invoke(agent_input)
            
            # Extract the output
            output = result.get("output", "No output generated")
            
            logger.info("Query processed successfully")
            return output
            
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(error_msg)
            return f"❌ {error_msg}"
    
    def test_agent(self) -> bool:
        """
        Test basic agent functionality.
        
        Returns:
            bool: True if agent test passes
        """
        try:
            # Test with a simple style query
            response = self.invoke("What styles are available in Recraft?")
            
            # Check if response contains style information
            success = "style" in response.lower() and len(response) > 50
            
            if success:
                logger.info("Agent test passed successfully")
            else:
                logger.warning("Agent test returned unexpected response")
                
            return success
            
        except Exception as e:
            logger.error(f"Agent test failed: {str(e)}")
            return False
    
    def get_capabilities(self) -> Dict[str, Any]:
        """
        Get agent capabilities and status.
        
        Returns:
            dict: Capabilities and status information
        """
        try:
            return {
                "name": "Recraft Image Generation Agent",
                "version": "1.0.0",
                "capabilities": {
                    "image_generation": True,
                    "vector_graphics": Settings.VECTOR_GENERATION_ENABLED,
                    "image_processing": True,
                    "style_guidance": True,
                    "multi_format_output": True
                },
                "supported_formats": {
                    "input": Settings.SUPPORTED_FORMATS,
                    "vector_output": Settings.VECTOR_OUTPUT_FORMATS if Settings.VECTOR_GENERATION_ENABLED else []
                },
                "tools": [tool.name for tool in self.tools],
                "llm_model": Settings.LANGUAGE_MODEL,
                "recraft_model": Settings.RECRAFT_MODEL,
                "status": {
                    "llm_ready": self.llm_integration.test_connectivity(),
                    "recraft_ready": self._test_recraft_connection(),
                    "vector_ready": self._test_vector_processing()
                }
            }
        except Exception as e:
            logger.error(f"Failed to get capabilities: {str(e)}")
            return {"error": str(e)}
    
    def _test_recraft_connection(self) -> bool:
        """Test Recraft API connection."""
        try:
            return self.recraft_image_tool.client.test_connection()
        except Exception:
            return False
    
    def _test_vector_processing(self) -> bool:
        """Test vector processing capabilities."""
        try:
            return self.vector_processor.test_vector_processing()
        except Exception:
            return False


# Alias for compatibility with the existing architecture
ImageGenerationAgent = RecraftAgent