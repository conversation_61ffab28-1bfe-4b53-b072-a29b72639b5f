"""
Package initialization for Image Agent web framework.
"""

# Version information
__version__ = "1.0.0"
__author__ = "Image Agent Team"

# Web components
try:
    from .src.base_flask_app import BaseFlaskApp, APIRoutes, WebSocketManager
    from .src.template_generator import (
        TemplateGenerator, 
        OpenAITemplateGenerator,
        RecraftTemplateGenerator,
        ImagenTemplateGenerator,
        FluxTemplateGenerator,
        create_templates_for_provider,
        create_static_assets
    )
    
    __all__ = [
        'BaseFlaskApp',
        'APIRoutes',
        'WebSocketManager',
        'TemplateGenerator',
        'OpenAITemplateGenerator',
        'RecraftTemplateGenerator', 
        'ImagenTemplateGenerator',
        'FluxTemplateGenerator',
        'create_templates_for_provider',
        'create_static_assets',
    ]
    
except ImportError as e:
    # Handle import errors gracefully during development
    print(f"Warning: Failed to import some web components: {e}")
    __all__ = []