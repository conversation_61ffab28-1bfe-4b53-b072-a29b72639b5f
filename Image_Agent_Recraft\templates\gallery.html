{% extends "base.html" %}

{% block title %}Gallery - Recraft Vector Graphics Agent{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-images me-2"></i>Generated Images Gallery
                    </h2>
                    <p class="mb-0 text-muted">Browse your created images and vector graphics</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="refreshGallery()">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setView('grid')" id="grid-view">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm active" onclick="setView('list')" id="list-view">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter and Search -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search-input" placeholder="Search images..." onkeyup="filterImages()">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="type-filter" onchange="filterImages()">
                            <option value="all">All Types</option>
                            <option value="vector">Vector Graphics</option>
                            <option value="raster">Raster Images</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="sort-filter" onchange="sortImages()">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="name">Name A-Z</option>
                            <option value="size">Size (Large to Small)</option>
                        </select>
                    </div>
                </div>
                
                <!-- Gallery Content -->
                <div id="gallery-content">
                    {% if images %}
                        <div class="row" id="images-container">
                            {% for image in images %}
                            <div class="col-lg-4 col-md-6 mb-4 image-item" 
                                 data-name="{{ image.filename }}" 
                                 data-type="{% if image.is_vector %}vector{% else %}raster{% endif %}"
                                 data-created="{{ image.created }}"
                                 data-size="{{ image.size }}">
                                <div class="card h-100">
                                    <div class="position-relative">
                                        <img src="/generated_images/{{ image.filename }}" 
                                             class="card-img-top" 
                                             style="height: 200px; object-fit: cover;"
                                             alt="{{ image.filename }}"
                                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNODcuNSA2Mi41Qzg3LjUgNzAuNzg0MyA4MC43ODQzIDc3LjUgNzIuNSA3Ny41QzY0LjIxNTcgNzcuNSA1Ny41IDcwLjc4NDMgNTcuNSA2Mi41QzU3LjUgNTQuMjE1NyA2NC4yMTU3IDQ3LjUgNzIuNSA0Ny41QzgwLjc4NDMgNDcuNSA4Ny41IDU0LjIxNTcgODcuNSA2Mi41WiIgZmlsbD0iIzk0QTNCOCIvPjxwYXRoIGQ9Ik00NS4wIDEzNy41SDE1NS4wTDE0MC4wIDExMi41TDExNS4wIDEzNy41TDEwMC4wIDEyMi41TDY1LjAgMTUyLjVINDUuMFYxMzcuNVoiIGZpbGw9IiM5NEEzQjgiLz48L3N2Zz4='"
                                             loading="lazy">
                                        
                                        {% if image.is_vector %}
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-success">
                                                <i class="fas fa-vector-square me-1"></i>Vector
                                            </span>
                                        </div>
                                        {% endif %}
                                        
                                        <div class="position-absolute bottom-0 start-0 end-0 p-2 bg-dark bg-opacity-75 text-white">
                                            <small>{{ image.created }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="card-body">
                                        <h6 class="card-title text-truncate" title="{{ image.filename }}">
                                            {{ image.filename }}
                                        </h6>
                                        <p class="card-text small text-muted">
                                            Size: {{ "%.1f"|format(image.size / 1024) }} KB
                                        </p>
                                        
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm flex-fill" 
                                                    onclick="viewImage('{{ image.filename }}')">
                                                <i class="fas fa-eye me-1"></i>View
                                            </button>
                                            <button class="btn btn-outline-success btn-sm flex-fill" 
                                                    onclick="downloadImage('{{ image.filename }}')">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        type="button" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="shareImage('{{ image.filename }}')">
                                                        <i class="fas fa-share me-2"></i>Share
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="editImage('{{ image.filename }}')">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteImage('{{ image.filename }}')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-images text-muted fa-4x mb-3"></i>
                            <h4 class="text-muted">No Images Yet</h4>
                            <p class="text-muted mb-4">Start creating amazing images and vector graphics!</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="/" class="btn btn-recraft">
                                    <i class="fas fa-magic me-2"></i>Generate Images
                                </a>
                                <a href="/vector" class="btn btn-outline-recraft">
                                    <i class="fas fa-bezier-curve me-2"></i>Create Vectors
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" class="img-fluid rounded" alt="Full size preview">
                <div class="mt-3">
                    <div id="modalInfo" class="text-muted small"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="modalDownload">
                    <i class="fas fa-download me-1"></i>Download
                </button>
                <button type="button" class="btn btn-recraft" id="modalEdit">
                    <i class="fas fa-edit me-1"></i>Edit/Process
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Card -->
{% if images %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Gallery Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="h3 text-primary">{{ images|length }}</div>
                        <div class="text-muted">Total Images</div>
                    </div>
                    <div class="col-md-3">
                        <div class="h3 text-success">{{ images|selectattr("is_vector")|list|length }}</div>
                        <div class="text-muted">Vector Graphics</div>
                    </div>
                    <div class="col-md-3">
                        <div class="h3 text-info">{{ images|rejectattr("is_vector")|list|length }}</div>
                        <div class="text-muted">Raster Images</div>
                    </div>
                    <div class="col-md-3">
                        <div class="h3 text-warning">{{ "%.1f"|format((images|sum(attribute="size"))/1024/1024) }} MB</div>
                        <div class="text-muted">Total Size</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
let currentView = 'grid';

function setView(viewType) {
    currentView = viewType;
    
    // Update button states
    document.getElementById('grid-view').classList.toggle('active', viewType === 'grid');
    document.getElementById('list-view').classList.toggle('active', viewType === 'list');
    
    const container = document.getElementById('images-container');
    const items = container.querySelectorAll('.image-item');
    
    if (viewType === 'list') {
        // List view
        items.forEach(item => {
            item.className = 'col-12 mb-3 image-item';
            const card = item.querySelector('.card');
            card.classList.add('d-md-flex', 'flex-row');
            
            const img = item.querySelector('.card-img-top');
            img.style.width = '200px';
            img.style.height = '150px';
        });
    } else {
        // Grid view
        items.forEach(item => {
            item.className = 'col-lg-4 col-md-6 mb-4 image-item';
            const card = item.querySelector('.card');
            card.classList.remove('d-md-flex', 'flex-row');
            
            const img = item.querySelector('.card-img-top');
            img.style.width = 'auto';
            img.style.height = '200px';
        });
    }
}

function filterImages() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const typeFilter = document.getElementById('type-filter').value;
    
    const items = document.querySelectorAll('.image-item');
    
    items.forEach(item => {
        const name = item.dataset.name.toLowerCase();
        const type = item.dataset.type;
        
        const matchesSearch = name.includes(searchTerm);
        const matchesType = typeFilter === 'all' || type === typeFilter;
        
        item.style.display = matchesSearch && matchesType ? 'block' : 'none';
    });
    
    updateResultsCount();
}

function sortImages() {
    const sortBy = document.getElementById('sort-filter').value;
    const container = document.getElementById('images-container');
    const items = Array.from(container.querySelectorAll('.image-item'));
    
    items.sort((a, b) => {
        switch (sortBy) {
            case 'newest':
                return new Date(b.dataset.created) - new Date(a.dataset.created);
            case 'oldest':
                return new Date(a.dataset.created) - new Date(b.dataset.created);
            case 'name':
                return a.dataset.name.localeCompare(b.dataset.name);
            case 'size':
                return parseInt(b.dataset.size) - parseInt(a.dataset.size);
            default:
                return 0;
        }
    });
    
    // Remove and re-append sorted items
    items.forEach(item => container.appendChild(item));
}

function updateResultsCount() {
    const visibleItems = document.querySelectorAll('.image-item[style="display: block;"], .image-item:not([style*="display: none"])');
    const totalItems = document.querySelectorAll('.image-item').length;
    
    // You could add a results counter here if needed
}

function viewImage(filename) {
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalInfo = document.getElementById('modalInfo');
    const modalDownload = document.getElementById('modalDownload');
    const modalEdit = document.getElementById('modalEdit');
    
    modalImage.src = `/generated_images/${filename}`;
    modalTitle.textContent = filename;
    
    // Find image data
    const item = document.querySelector(`[data-name="${filename}"]`);
    if (item) {
        const type = item.dataset.type === 'vector' ? 'Vector Graphics' : 'Raster Image';
        const size = formatFileSize(parseInt(item.dataset.size));
        const created = item.dataset.created;
        
        modalInfo.innerHTML = `
            <div class="d-flex justify-content-center gap-4">
                <span><strong>Type:</strong> ${type}</span>
                <span><strong>Size:</strong> ${size}</span>
                <span><strong>Created:</strong> ${created}</span>
            </div>
        `;
    }
    
    // Set up download button
    modalDownload.onclick = () => downloadImage(filename);
    modalEdit.onclick = () => {
        modal.hide();
        editImage(filename);
    };
    
    modal.show();
}

function downloadImage(filename) {
    const link = document.createElement('a');
    link.href = `/generated_images/${filename}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showToast(`Downloading ${filename}`, 'success');
}

function shareImage(filename) {
    const url = `${window.location.origin}/generated_images/${filename}`;
    
    if (navigator.share) {
        navigator.share({
            title: `Generated Image: ${filename}`,
            url: url
        }).then(() => {
            showToast('Image shared successfully!', 'success');
        }).catch((error) => {
            copyToClipboard(url);
        });
    } else {
        copyToClipboard(url);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Image URL copied to clipboard!', 'success');
    }).catch(() => {
        showToast('Unable to copy URL', 'error');
    });
}

function editImage(filename) {
    // Redirect to processing page with the image
    showToast(`Opening ${filename} for editing...`, 'info');
    setTimeout(() => {
        window.location.href = '/processing';
    }, 1000);
}

function deleteImage(filename) {
    if (confirm(`Are you sure you want to delete "${filename}"? This action cannot be undone.`)) {
        // This would implement actual deletion
        showToast('Delete functionality will be available soon!', 'info');
    }
}

function refreshGallery() {
    showToast('Refreshing gallery...', 'info');
    setTimeout(() => {
        location.reload();
    }, 500);
}

// Initialize gallery
document.addEventListener('DOMContentLoaded', function() {
    // Set initial view
    setView('grid');
    
    // Initialize any tooltips or other components
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}