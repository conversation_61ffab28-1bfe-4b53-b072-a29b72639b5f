<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Agent Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-paint-brush me-2"></i>
                Multi-Provider Image Agent
            </a>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Provider Selection -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Choose AI Provider</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="provider-card" onclick="selectProvider('openai')">
                                    <div class="provider-icon bg-success">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <h6>OpenAI DALL-E</h6>
                                    <small class="text-muted">Advanced AI art generation</small>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="provider-card" onclick="selectProvider('recraft')">
                                    <div class="provider-icon bg-danger">
                                        <i class="fas fa-vector-square"></i>
                                    </div>
                                    <h6>Recraft AI</h6>
                                    <small class="text-muted">Vector graphics specialist</small>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="provider-card" onclick="selectProvider('imagen')">
                                    <div class="provider-icon bg-primary">
                                        <i class="fab fa-google"></i>
                                    </div>
                                    <h6>Google Imagen</h6>
                                    <small class="text-muted">Photorealistic images</small>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="provider-card" onclick="selectProvider('flux')">
                                    <div class="provider-icon bg-warning">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <h6>FLUX.1</h6>
                                    <small class="text-muted">Lightning fast generation</small>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Selected Provider: <strong id="selectedProvider">None</strong>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Generation Form -->
                <div class="card" id="generationForm" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-magic me-2"></i>Generate Image</h5>
                    </div>
                    <div class="card-body">
                        <form onsubmit="generateImage(event)">
                            <div class="mb-3">
                                <label for="prompt" class="form-label">Image Description</label>
                                <textarea class="form-control" id="prompt" rows="3" 
                                         placeholder="Describe the image you want to generate..." required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="width" class="form-label">Width</label>
                                        <select class="form-select" id="width">
                                            <option value="512">512px</option>
                                            <option value="768">768px</option>
                                            <option value="1024">1024px</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="height" class="form-label">Height</label>
                                        <select class="form-select" id="height">
                                            <option value="512">512px</option>
                                            <option value="768">768px</option>
                                            <option value="1024">1024px</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="style" class="form-label">Style</label>
                                        <select class="form-select" id="style">
                                            <option value="auto">Auto</option>
                                            <option value="realistic">Realistic</option>
                                            <option value="artistic">Artistic</option>
                                            <option value="cartoon">Cartoon</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="quality" class="form-label">Quality</label>
                                        <select class="form-select" id="quality">
                                            <option value="standard">Standard</option>
                                            <option value="high">High</option>
                                            <option value="ultra">Ultra</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                                    <i class="fas fa-magic me-2"></i>Generate Image
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Results Area -->
                <div id="resultsArea"></div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>