"""
Shared validation utilities for all Image Agent providers.
Provides input validation, sanitization, and security checks.
"""
import re
import os
from typing import Any, Dict, List, Optional, Union, Tuple
from urllib.parse import urlparse
import mimetypes
import logging

logger = logging.getLogger(__name__)


class InputValidator:
    """Validates and sanitizes user inputs."""
    
    # Common validation patterns
    PATTERNS = {
        'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
        'safe_filename': re.compile(r'^[a-zA-Z0-9._-]+$'),
        'alphanumeric': re.compile(r'^[a-zA-Z0-9]+$'),
        'prompt_safe': re.compile(r'^[a-zA-Z0-9\s.,!?;:()\[\]{}\-_\'\"]+$'),
        'url': re.compile(r'^https?://[^\s/$.?#].[^\s]*$', re.IGNORECASE),
        'api_key': re.compile(r'^[a-zA-Z0-9._-]+$'),
    }
    
    # Dangerous content patterns to filter
    DANGEROUS_PATTERNS = [
        re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
        re.compile(r'javascript:', re.IGNORECASE),
        re.compile(r'vbscript:', re.IGNORECASE),
        re.compile(r'onload\s*=', re.IGNORECASE),
        re.compile(r'onerror\s*=', re.IGNORECASE),
    ]
    
    @staticmethod
    def validate_prompt(prompt: str, max_length: int = 2000, min_length: int = 1) -> Tuple[bool, str]:
        """
        Validate image generation prompt.
        
        Args:
            prompt: User prompt to validate
            max_length: Maximum allowed length
            min_length: Minimum required length
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(prompt, str):
            return False, "Prompt must be a string"
        
        prompt = prompt.strip()
        
        if len(prompt) < min_length:
            return False, f"Prompt must be at least {min_length} characters"
        
        if len(prompt) > max_length:
            return False, f"Prompt must be no more than {max_length} characters"
        
        # Check for dangerous content
        for pattern in InputValidator.DANGEROUS_PATTERNS:
            if pattern.search(prompt):
                return False, "Prompt contains potentially unsafe content"
        
        # Check for basic safety (allow most characters for creative prompts)
        if not re.match(r'^[^<>{}|\\^`\[\]]*$', prompt):
            return False, "Prompt contains invalid characters"
        
        logger.debug(f"Validated prompt: {len(prompt)} characters")
        return True, ""
    
    @staticmethod
    def validate_image_size(size: Union[str, Tuple[int, int]], allowed_sizes: List[str] = None) -> Tuple[bool, str]:
        """
        Validate image size specification.
        
        Args:
            size: Size specification (e.g., "1024x1024" or (1024, 1024))
            allowed_sizes: List of allowed size strings
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if allowed_sizes is None:
            allowed_sizes = ["256x256", "512x512", "1024x1024", "1024x1792", "1792x1024"]
        
        # Convert tuple to string if needed
        if isinstance(size, tuple) and len(size) == 2:
            size = f"{size[0]}x{size[1]}"
        
        if not isinstance(size, str):
            return False, "Size must be a string in format 'WIDTHxHEIGHT'"
        
        # Check if it's in allowed sizes
        if size not in allowed_sizes:
            return False, f"Size must be one of: {', '.join(allowed_sizes)}"
        
        # Parse and validate dimensions
        try:
            width_str, height_str = size.split('x')
            width = int(width_str)
            height = int(height_str)
            
            if width <= 0 or height <= 0:
                return False, "Width and height must be positive"
            
            if width > 4096 or height > 4096:
                return False, "Dimensions cannot exceed 4096 pixels"
            
            # Check aspect ratio limits
            aspect_ratio = width / height
            if aspect_ratio < 0.25 or aspect_ratio > 4.0:
                return False, "Aspect ratio must be between 1:4 and 4:1"
            
        except ValueError:
            return False, "Invalid size format. Use 'WIDTHxHEIGHT' (e.g., '1024x1024')"
        
        logger.debug(f"Validated image size: {size}")
        return True, ""
    
    @staticmethod
    def validate_filename(filename: str, max_length: int = 255) -> Tuple[bool, str]:
        """
        Validate and sanitize filename.
        
        Args:
            filename: Filename to validate
            max_length: Maximum filename length
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(filename, str):
            return False, "Filename must be a string"
        
        if not filename.strip():
            return False, "Filename cannot be empty"
        
        if len(filename) > max_length:
            return False, f"Filename too long (max {max_length} characters)"
        
        # Check for dangerous patterns
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        if any(char in filename for char in dangerous_chars):
            return False, "Filename contains invalid characters"
        
        # Check for reserved names (Windows)
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        
        name_without_ext = os.path.splitext(filename)[0].upper()
        if name_without_ext in reserved_names:
            return False, f"Filename '{filename}' is a reserved system name"
        
        logger.debug(f"Validated filename: {filename}")
        return True, ""
    
    @staticmethod
    def validate_file_type(filename: str, allowed_types: List[str] = None) -> Tuple[bool, str]:
        """
        Validate file type based on extension.
        
        Args:
            filename: Filename to check
            allowed_types: List of allowed extensions (e.g., ['.jpg', '.png'])
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if allowed_types is None:
            allowed_types = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        
        if not isinstance(filename, str):
            return False, "Filename must be a string"
        
        # Get file extension
        _, ext = os.path.splitext(filename.lower())
        
        if not ext:
            return False, "File must have an extension"
        
        if ext not in [t.lower() for t in allowed_types]:
            return False, f"File type '{ext}' not allowed. Allowed types: {', '.join(allowed_types)}"
        
        logger.debug(f"Validated file type: {ext}")
        return True, ""
    
    @staticmethod
    def validate_api_key(api_key: str, min_length: int = 10, max_length: int = 200) -> Tuple[bool, str]:
        """
        Validate API key format.
        
        Args:
            api_key: API key to validate
            min_length: Minimum key length
            max_length: Maximum key length
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(api_key, str):
            return False, "API key must be a string"
        
        if not api_key.strip():
            return False, "API key cannot be empty"
        
        if len(api_key) < min_length:
            return False, f"API key too short (minimum {min_length} characters)"
        
        if len(api_key) > max_length:
            return False, f"API key too long (maximum {max_length} characters)"
        
        # Check for valid characters (alphanumeric, dots, dashes, underscores)
        if not InputValidator.PATTERNS['api_key'].match(api_key):
            return False, "API key contains invalid characters"
        
        logger.debug("Validated API key format")
        return True, ""
    
    @staticmethod
    def validate_url(url: str) -> Tuple[bool, str]:
        """
        Validate URL format and safety.
        
        Args:
            url: URL to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(url, str):
            return False, "URL must be a string"
        
        if not url.strip():
            return False, "URL cannot be empty"
        
        # Basic URL format check
        if not InputValidator.PATTERNS['url'].match(url):
            return False, "Invalid URL format"
        
        try:
            parsed = urlparse(url)
            
            # Must have scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                return False, "URL must include protocol and domain"
            
            # Only allow http/https
            if parsed.scheme not in ['http', 'https']:
                return False, "URL must use http or https protocol"
            
            # Basic domain validation
            if '.' not in parsed.netloc:
                return False, "URL must have a valid domain"
            
        except Exception:
            return False, "Invalid URL format"
        
        logger.debug(f"Validated URL: {url}")
        return True, ""
    
    @staticmethod
    def sanitize_string(text: str, max_length: int = 1000, allow_html: bool = False) -> str:
        """
        Sanitize user input string.
        
        Args:
            text: Text to sanitize
            max_length: Maximum allowed length
            allow_html: Whether to allow HTML tags
            
        Returns:
            Sanitized string
        """
        if not isinstance(text, str):
            return ""
        
        # Trim whitespace
        text = text.strip()
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        if not allow_html:
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', text)
            
            # Remove dangerous patterns
            for pattern in InputValidator.DANGEROUS_PATTERNS:
                text = pattern.sub('', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    @staticmethod
    def validate_json_structure(data: Any, required_fields: List[str] = None) -> Tuple[bool, str]:
        """
        Validate JSON data structure.
        
        Args:
            data: Data to validate
            required_fields: List of required field names
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if required_fields is None:
            required_fields = []
        
        if not isinstance(data, dict):
            return False, "Data must be a JSON object"
        
        # Check required fields
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field: {field}"
        
        logger.debug("Validated JSON structure")
        return True, ""


class SecurityValidator:
    """Advanced security validation utilities."""
    
    @staticmethod
    def validate_file_content(file_path: str, max_size: int = 16 * 1024 * 1024) -> Tuple[bool, str]:
        """
        Validate file content for security.
        
        Args:
            file_path: Path to file to validate
            max_size: Maximum allowed file size in bytes
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not os.path.exists(file_path):
            return False, "File does not exist"
        
        try:
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > max_size:
                return False, f"File too large ({file_size} bytes, max {max_size})"
            
            if file_size == 0:
                return False, "File is empty"
            
            # Check MIME type
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type and not mime_type.startswith('image/'):
                return False, f"Invalid file type: {mime_type}"
            
        except Exception as e:
            return False, f"Error validating file: {str(e)}"
        
        logger.debug(f"Validated file content: {file_path}")
        return True, ""
    
    @staticmethod
    def scan_for_malicious_content(text: str) -> Tuple[bool, List[str]]:
        """
        Scan text for potentially malicious content.
        
        Args:
            text: Text to scan
            
        Returns:
            Tuple of (is_safe, list_of_issues)
        """
        issues = []
        
        # Check for script injection
        if re.search(r'<script[^>]*>.*?</script>', text, re.IGNORECASE | re.DOTALL):
            issues.append("Contains script tags")
        
        # Check for common injection patterns
        injection_patterns = [
            r'javascript:',
            r'vbscript:',
            r'data:text/html',
            r'<iframe[^>]*>',
            r'<object[^>]*>',
            r'<embed[^>]*>',
            r'on\w+\s*=',  # Event handlers
        ]
        
        for pattern in injection_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                issues.append(f"Contains potentially dangerous pattern: {pattern}")
        
        # Check for SQL injection attempts
        sql_patterns = [
            r"('|(\\'))+(.*)(--)",
            r"(\s*)(;)(\s*)(drop|delete|update|insert|create|alter)",
            r"(union)(.+)(select)",
        ]
        
        for pattern in sql_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                issues.append("Contains potential SQL injection")
                break
        
        is_safe = len(issues) == 0
        if not is_safe:
            logger.warning(f"Malicious content detected: {issues}")
        
        return is_safe, issues
    
    @staticmethod
    def validate_path_traversal(path: str, base_path: str = None) -> Tuple[bool, str]:
        """
        Validate path to prevent directory traversal attacks.
        
        Args:
            path: Path to validate
            base_path: Base path that access should be restricted to
            
        Returns:
            Tuple of (is_safe, error_message)
        """
        if not isinstance(path, str):
            return False, "Path must be a string"
        
        # Check for obvious traversal attempts
        if '..' in path or '~' in path:
            return False, "Path contains directory traversal characters"
        
        # Normalize path
        try:
            normalized_path = os.path.normpath(path)
            
            # Check if path tries to go outside base path
            if base_path:
                base_path = os.path.normpath(base_path)
                if not normalized_path.startswith(base_path):
                    return False, "Path attempts to access outside allowed directory"
            
        except Exception:
            return False, "Invalid path format"
        
        logger.debug(f"Validated path: {normalized_path}")
        return True, ""


class ContentFilter:
    """Content filtering and moderation utilities."""
    
    # Common inappropriate content keywords
    INAPPROPRIATE_KEYWORDS = [
        'violence', 'explicit', 'nsfw', 'adult', 'sexual',
        'hate', 'discrimination', 'harassment', 'illegal',
        'drugs', 'weapons', 'terrorism', 'suicide'
    ]
    
    @staticmethod
    def check_content_appropriateness(text: str, strict_mode: bool = False) -> Tuple[bool, List[str]]:
        """
        Check if content is appropriate.
        
        Args:
            text: Text content to check
            strict_mode: Whether to use strict filtering
            
        Returns:
            Tuple of (is_appropriate, list_of_flags)
        """
        flags = []
        text_lower = text.lower()
        
        # Check for inappropriate keywords
        for keyword in ContentFilter.INAPPROPRIATE_KEYWORDS:
            if keyword in text_lower:
                flags.append(f"Contains inappropriate keyword: {keyword}")
        
        if strict_mode:
            # Additional strict checks
            sensitive_patterns = [
                r'\b(naked|nude|sex|porn)\b',
                r'\b(kill|murder|violence|blood)\b',
                r'\b(drugs|cocaine|heroin|meth)\b',
            ]
            
            for pattern in sensitive_patterns:
                if re.search(pattern, text_lower):
                    flags.append(f"Contains sensitive content")
                    break
        
        is_appropriate = len(flags) == 0
        if not is_appropriate:
            logger.warning(f"Inappropriate content detected: {flags}")
        
        return is_appropriate, flags