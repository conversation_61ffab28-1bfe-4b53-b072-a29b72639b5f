"""
Demo application showing how to use the shared-components framework
to create a simple multi-provider image generation interface.
"""
import os
import sys
from pathlib import Path

# Add shared components to path
shared_components_path = Path(__file__).parent / 'shared-components'
sys.path.insert(0, str(shared_components_path))

try:
    from image_agent_web.src.template_generator import create_templates_for_provider, create_static_assets
    from image_agent_core.src.base_settings import CommonPaths, LoggingConfig
    
    # Mock Flask app for demonstration
    class DemoApp:
        """Demo application using shared components."""
        
        def __init__(self, provider: str = 'demo'):
            self.provider = provider
            self.paths = CommonPaths(Path(__file__).parent)
            
            # Set up logging
            LoggingConfig.setup_logging('INFO')
            
            # Ensure directories exist
            self.paths.ensure_all_directories()
            
            print(f"🚀 Initializing {provider.title()} Image Agent Demo")
            print(f"📁 Base directory: {self.paths.base_dir}")
            print(f"📊 Templates directory: {self.paths.templates_dir}")
            print(f"🎨 Static files directory: {self.paths.static_dir}")
        
        def setup_web_interface(self):
            """Set up web interface with templates and static assets."""
            print("\n📝 Creating templates...")
            
            # Create templates for the provider
            if self.provider.lower() in ['openai', 'recraft', 'imagen', 'flux']:
                templates = create_templates_for_provider(
                    self.provider.lower(), 
                    str(self.paths.templates_dir)
                )
                
                for template_name, file_path in templates.items():
                    print(f"   ✓ {template_name} -> {file_path}")
            else:
                print(f"   ⚠️  Unknown provider '{self.provider}', using default templates")
            
            # Create static assets
            print("\n🎨 Creating static assets...")
            assets = create_static_assets(str(self.paths.static_dir))
            
            for asset_name, file_path in assets.items():
                print(f"   ✓ {asset_name} -> {file_path}")
        
        def create_demo_config(self):
            """Create demo configuration files."""
            print("\n⚙️  Creating configuration...")
            
            # Create .env template
            env_template_path = self.paths.base_dir / '.env.example'
            env_content = f"""# {self.provider.title()} Image Agent Configuration
# Copy this file to .env and fill in your values

# API Configuration
{self.provider.upper()}_API_KEY=your_api_key_here
{self.provider.upper()}_API_BASE=
{self.provider.upper()}_TIMEOUT=30
{self.provider.upper()}_MAX_RETRIES=3

# Web Interface
SECRET_KEY=your_secret_key_here
DEBUG=false
PORT=5000

# File Handling
MAX_UPLOAD_SIZE=16777216
UPLOAD_EXTENSIONS=.jpg,.jpeg,.png,.gif,.webp,.bmp

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/image_agent.log
"""
            
            with open(env_template_path, 'w') as f:
                f.write(env_content)
            print(f"   ✓ Environment template -> {env_template_path}")
            
            # Create basic config.json
            config_path = self.paths.config_dir / 'config.json'
            config_content = {
                "provider": self.provider,
                "default_width": 512,
                "default_height": 512,
                "default_style": "auto",
                "default_quality": "standard",
                "output_format": "png",
                "features": {
                    "web_interface": True,
                    "cli_interface": True,
                    "history_tracking": True,
                    "file_uploads": True
                }
            }
            
            import json
            with open(config_path, 'w') as f:
                json.dump(config_content, f, indent=2)
            print(f"   ✓ Configuration file -> {config_path}")
        
        def create_demo_files(self):
            """Create demo/example files."""
            print("\n📄 Creating demo files...")
            
            # Create a simple main.py file
            main_py_path = self.paths.base_dir / 'main.py'
            main_py_content = f'''"""
{self.provider.title()} Image Agent - Main Application
Generated using shared-components framework
"""

import os
import sys
from pathlib import Path

# Add shared components to Python path
shared_path = Path(__file__).parent / 'shared-components'
sys.path.insert(0, str(shared_path))

try:
    from image_agent_web import BaseFlaskApp
    from image_agent_core import FileManager, InputValidator
    
    class {self.provider.title()}ImageAgent(BaseFlaskApp):
        \"\"\"Custom {self.provider.title()} Image Agent application.\"\"\"
        
        def __init__(self):
            super().__init__(
                name='{self.provider}_image_agent',
                provider_name='{self.provider.title()}',
                template_folder='templates',
                static_folder='static'
            )
            
            # Add provider-specific routes
            self.add_custom_routes()
        
        def add_custom_routes(self):
            \"\"\"Add {self.provider}-specific routes.\"\"\"
            
            @self.app.route('/api/generate', methods=['POST'])
            @self.require_session
            def generate_image():
                # This would contain actual {self.provider} integration
                return {{"status": "demo", "message": "This is a demo implementation"}}
        
        def get_session_history(self, session_id: str):
            \"\"\"Get generation history for session.\"\"\"
            # This would query actual history from database/files
            return []
    
    if __name__ == '__main__':
        app = {self.provider.title()}ImageAgent()
        app.run(debug=True, port=5000)

except ImportError as e:
    print(f"Error importing shared components: {{e}}")
    print("Make sure to install required dependencies:")
    print("pip install flask pillow python-dotenv")
    sys.exit(1)
'''
            
            with open(main_py_path, 'w') as f:
                f.write(main_py_content)
            print(f"   ✓ Main application -> {main_py_path}")
            
            # Create requirements.txt
            requirements_path = self.paths.base_dir / 'requirements.txt'
            requirements_content = """# Core dependencies
flask>=2.0.0
pillow>=8.0.0
python-dotenv>=0.19.0
werkzeug>=2.0.0

# Optional dependencies for enhanced features
requests>=2.25.0
asyncio-throttle>=1.0.0
aiohttp>=3.8.0

# Development dependencies
pytest>=6.0.0
pytest-asyncio>=0.18.0
black>=22.0.0
flake8>=4.0.0
"""
            
            with open(requirements_path, 'w') as f:
                f.write(requirements_content)
            print(f"   ✓ Requirements file -> {requirements_path}")
            
            # Create README.md
            readme_path = self.paths.base_dir / 'README.md'
            readme_content = f"""# {self.provider.title()} Image Agent

AI-powered image generation using {self.provider.title()} API with a modern web interface.

## Features

- 🎨 Generate images from text descriptions
- 🖼️  Multiple image styles and qualities
- 💾 Download and save generated images
- 📱 Responsive web interface
- 🔒 Secure file handling and validation
- 📊 Generation history tracking
- ⚡ Real-time generation status updates

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your {self.provider.title()} API key
   ```

3. **Run the Application**
   ```bash
   python main.py
   ```

4. **Open in Browser**
   Navigate to http://localhost:5000

## Configuration

Edit `config/config.json` to customize default settings:

- Image dimensions
- Default style and quality
- File upload limits
- Feature toggles

## API Endpoints

- `GET /` - Main web interface
- `POST /api/generate` - Generate image
- `GET /api/history` - Get generation history
- `POST /api/upload` - Upload reference image
- `GET /health` - Health check

## Development

This application uses the shared-components framework for:

- 🔧 **Core utilities**: File management, validation, image processing
- 🌐 **Web framework**: Flask base app with common routes
- 🖥️  **CLI framework**: Command-line interface (future)

## Architecture

Built on the multi-provider architecture supporting:

- OpenAI DALL-E
- Google Imagen  
- FLUX.1
- Recraft AI

## License

MIT License - see LICENSE file for details.
"""
            
            with open(readme_path, 'w') as f:
                f.write(readme_content)
            print(f"   ✓ README file -> {readme_path}")
    
    def run_demo():
        """Run the complete demo setup."""
        print("🎯 Image Agent Shared-Components Demo")
        print("=" * 50)
        
        # Ask user for provider choice
        providers = ['openai', 'recraft', 'imagen', 'flux', 'demo']
        print(f"Available providers: {', '.join(providers)}")
        
        provider = input("\nChoose a provider (or press Enter for 'demo'): ").strip().lower()
        if not provider:
            provider = 'demo'
        
        if provider not in providers:
            print(f"⚠️  Unknown provider '{provider}', using 'demo'")
            provider = 'demo'
        
        # Create demo app
        app = DemoApp(provider)
        
        # Set up components
        app.setup_web_interface()
        app.create_demo_config()
        app.create_demo_files()
        
        # Success message
        print("\n🎉 Demo setup complete!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Configure your API key in .env")
        print("3. Run the application: python main.py")
        print("4. Open http://localhost:5000 in your browser")
        
        print(f"\n📁 All files created in: {app.paths.base_dir}")
        
        return True

except ImportError as e:
    print(f"❌ Cannot import shared components: {e}")
    print("\nThis demo requires the shared-components to be properly set up.")
    print("Make sure you're running this from the correct directory.")
    
    def run_demo():
        """Fallback demo function."""
        print("🎯 Image Agent Shared-Components Demo")
        print("=" * 50)
        print("❌ Shared components not available")
        print("\nTo set up shared components:")
        print("1. Ensure all files are in shared-components/")
        print("2. Fix any import issues in the core modules")
        print("3. Install required dependencies")
        return False

if __name__ == '__main__':
    success = run_demo()
    sys.exit(0 if success else 1)