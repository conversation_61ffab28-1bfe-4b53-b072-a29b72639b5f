"""
Shared image processing utilities for all Image Agent providers.
Provides common image encoding, decoding, and manipulation functions.
"""
import base64
import io
import os
from typing import Union, Tuple, Optional, Dict, Any
from PIL import Image, ImageOps, ImageEnhance
import logging

logger = logging.getLogger(__name__)


class ImageEncoder:
    """Handles image encoding, decoding, and basic manipulations."""
    
    SUPPORTED_FORMATS = {'PNG', 'JPEG', 'JPG', 'GIF', 'BMP', 'WEBP', 'SVG'}
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
    DEFAULT_QUALITY = 90
    
    @staticmethod
    def encode_image_to_base64(image_path: str) -> str:
        """
        Encode an image file to base64 string.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Base64 encoded string
            
        Raises:
            FileNotFoundError: If image file doesn't exist
            ValueError: If image format is not supported
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        # Check file size
        file_size = os.path.getsize(image_path)
        if file_size > ImageEncoder.MAX_FILE_SIZE:
            raise ValueError(f"File size ({file_size} bytes) exceeds maximum ({ImageEncoder.MAX_FILE_SIZE} bytes)")
        
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            logger.debug(f"Encoded image {image_path} to base64 ({len(encoded_string)} characters)")
            return encoded_string
    
    @staticmethod
    def decode_base64_to_image(base64_string: str, output_path: str) -> str:
        """
        Decode base64 string to image file.
        
        Args:
            base64_string: Base64 encoded image
            output_path: Path to save the decoded image
            
        Returns:
            Path to the saved image file
        """
        try:
            # Remove data URL prefix if present
            if base64_string.startswith('data:image/'):
                base64_string = base64_string.split(',', 1)[1]
            
            image_data = base64.b64decode(base64_string)
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'wb') as f:
                f.write(image_data)
            
            logger.debug(f"Decoded base64 to image file: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to decode base64 to image: {str(e)}")
            raise ValueError(f"Invalid base64 image data: {str(e)}")
    
    @staticmethod
    def get_image_info(image_path: str) -> Dict[str, Any]:
        """
        Get information about an image file.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with image information
        """
        try:
            with Image.open(image_path) as img:
                info = {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height,
                    'has_transparency': 'transparency' in img.info or img.mode in ('RGBA', 'LA'),
                    'file_size': os.path.getsize(image_path),
                    'aspect_ratio': round(img.width / img.height, 2) if img.height > 0 else 0
                }
                logger.debug(f"Got image info for {image_path}: {info}")
                return info
        except Exception as e:
            logger.error(f"Failed to get image info for {image_path}: {str(e)}")
            raise ValueError(f"Cannot read image file: {str(e)}")
    
    @staticmethod
    def resize_image(image_path: str, output_path: str, size: Tuple[int, int], 
                    maintain_aspect: bool = True, quality: int = DEFAULT_QUALITY) -> str:
        """
        Resize an image to specified dimensions.
        
        Args:
            image_path: Path to input image
            output_path: Path to output image
            size: Target size as (width, height)
            maintain_aspect: Whether to maintain aspect ratio
            quality: JPEG quality (1-100)
            
        Returns:
            Path to resized image
        """
        try:
            with Image.open(image_path) as img:
                # Convert RGBA to RGB if saving as JPEG
                if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
                    if img.mode in ('RGBA', 'LA'):
                        # Create white background
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                        img = background
                
                if maintain_aspect:
                    img.thumbnail(size, Image.Resampling.LANCZOS)
                else:
                    img = img.resize(size, Image.Resampling.LANCZOS)
                
                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # Save with appropriate quality
                save_kwargs = {'quality': quality, 'optimize': True}
                if img.format == 'PNG':
                    save_kwargs = {'optimize': True}
                
                img.save(output_path, **save_kwargs)
                logger.debug(f"Resized image {image_path} to {output_path} with size {size}")
                return output_path
                
        except Exception as e:
            logger.error(f"Failed to resize image: {str(e)}")
            raise ValueError(f"Cannot resize image: {str(e)}")
    
    @staticmethod
    def convert_format(image_path: str, output_path: str, target_format: str = 'PNG',
                      quality: int = DEFAULT_QUALITY) -> str:
        """
        Convert image to different format.
        
        Args:
            image_path: Path to input image
            output_path: Path to output image
            target_format: Target format (PNG, JPEG, etc.)
            quality: Quality for lossy formats
            
        Returns:
            Path to converted image
        """
        try:
            target_format = target_format.upper()
            if target_format not in ImageEncoder.SUPPORTED_FORMATS:
                raise ValueError(f"Unsupported target format: {target_format}")
            
            with Image.open(image_path) as img:
                # Handle transparency for JPEG
                if target_format in ('JPEG', 'JPG') and img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                
                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # Save with format-specific options
                save_kwargs = {}
                if target_format in ('JPEG', 'JPG'):
                    save_kwargs = {'quality': quality, 'optimize': True}
                elif target_format == 'PNG':
                    save_kwargs = {'optimize': True}
                elif target_format == 'WEBP':
                    save_kwargs = {'quality': quality, 'method': 6}
                
                img.save(output_path, format=target_format, **save_kwargs)
                logger.debug(f"Converted {image_path} to {target_format} format: {output_path}")
                return output_path
                
        except Exception as e:
            logger.error(f"Failed to convert image format: {str(e)}")
            raise ValueError(f"Cannot convert image format: {str(e)}")
    
    @staticmethod
    def enhance_image(image_path: str, output_path: str, **enhancements) -> str:
        """
        Apply basic image enhancements.
        
        Args:
            image_path: Path to input image
            output_path: Path to output image
            **enhancements: Enhancement parameters:
                - brightness: 0.5 to 2.0 (1.0 = no change)
                - contrast: 0.5 to 2.0 (1.0 = no change)
                - saturation: 0.0 to 2.0 (1.0 = no change)
                - sharpness: 0.0 to 2.0 (1.0 = no change)
                
        Returns:
            Path to enhanced image
        """
        try:
            with Image.open(image_path) as img:
                enhanced = img.copy()
                
                # Apply brightness adjustment
                if 'brightness' in enhancements:
                    enhancer = ImageEnhance.Brightness(enhanced)
                    enhanced = enhancer.enhance(enhancements['brightness'])
                
                # Apply contrast adjustment
                if 'contrast' in enhancements:
                    enhancer = ImageEnhance.Contrast(enhanced)
                    enhanced = enhancer.enhance(enhancements['contrast'])
                
                # Apply color/saturation adjustment
                if 'saturation' in enhancements:
                    enhancer = ImageEnhance.Color(enhanced)
                    enhanced = enhancer.enhance(enhancements['saturation'])
                
                # Apply sharpness adjustment
                if 'sharpness' in enhancements:
                    enhancer = ImageEnhance.Sharpness(enhanced)
                    enhanced = enhancer.enhance(enhancements['sharpness'])
                
                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                enhanced.save(output_path, quality=ImageEncoder.DEFAULT_QUALITY, optimize=True)
                logger.debug(f"Enhanced image {image_path} and saved to {output_path}")
                return output_path
                
        except Exception as e:
            logger.error(f"Failed to enhance image: {str(e)}")
            raise ValueError(f"Cannot enhance image: {str(e)}")
    
    @staticmethod
    def create_thumbnail(image_path: str, output_path: str, size: Tuple[int, int] = (256, 256)) -> str:
        """
        Create a thumbnail of the image.
        
        Args:
            image_path: Path to input image
            output_path: Path to thumbnail
            size: Thumbnail size
            
        Returns:
            Path to thumbnail
        """
        return ImageEncoder.resize_image(image_path, output_path, size, maintain_aspect=True, quality=85)
    
    @staticmethod
    def validate_image_file(image_path: str) -> bool:
        """
        Validate if file is a valid image.
        
        Args:
            image_path: Path to image file
            
        Returns:
            True if valid image, False otherwise
        """
        try:
            with Image.open(image_path) as img:
                img.verify()  # This will raise an exception if not valid
            return True
        except Exception as e:
            logger.debug(f"Image validation failed for {image_path}: {str(e)}")
            return False
    
    @staticmethod
    def get_data_url(image_path: str) -> str:
        """
        Create a data URL from an image file.
        
        Args:
            image_path: Path to image file
            
        Returns:
            Data URL string
        """
        info = ImageEncoder.get_image_info(image_path)
        format_lower = info['format'].lower()
        
        # Map PIL formats to MIME types
        mime_mapping = {
            'jpeg': 'image/jpeg',
            'jpg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'webp': 'image/webp'
        }
        
        mime_type = mime_mapping.get(format_lower, 'image/png')
        base64_data = ImageEncoder.encode_image_to_base64(image_path)
        
        return f"data:{mime_type};base64,{base64_data}"


class ImageValidator:
    """Validation utilities for image processing."""
    
    @staticmethod
    def validate_dimensions(width: int, height: int, max_width: int = 4096, max_height: int = 4096) -> bool:
        """Validate image dimensions."""
        return 0 < width <= max_width and 0 < height <= max_height
    
    @staticmethod
    def validate_file_size(file_path: str, max_size: int = ImageEncoder.MAX_FILE_SIZE) -> bool:
        """Validate file size."""
        try:
            return os.path.getsize(file_path) <= max_size
        except OSError:
            return False
    
    @staticmethod
    def validate_format(format_name: str) -> bool:
        """Validate image format."""
        return format_name.upper() in ImageEncoder.SUPPORTED_FORMATS
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe storage."""
        import re
        # Remove or replace unsafe characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        name, ext = os.path.splitext(filename)
        if len(name) > 100:
            name = name[:100]
        return f"{name}{ext}"