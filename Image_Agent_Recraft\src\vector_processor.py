"""
Vector graphics processor for Recraft Image Agent.
Handles conversion between raster and vector formats, optimization, and processing.
"""
import os
import logging
from typing import Optional, Union, Dict, Any
from PIL import Image
import tempfile
from config.settings import Settings

# Setup logging
logger = logging.getLogger(__name__)


class VectorProcessingError(Exception):
    """Custom exception for vector processing errors."""
    pass


class VectorProcessor:
    """
    Processor for vector graphics operations including format conversion,
    optimization, and quality enhancement.
    """
    
    def __init__(self):
        """Initialize the vector processor."""
        self.supported_input_formats = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp']
        self.supported_vector_formats = ['svg', 'pdf', 'eps']
        
        # Check for required dependencies
        self._check_dependencies()
        
        logger.info("VectorProcessor initialized successfully")
    
    def _check_dependencies(self):
        """Check for required dependencies and log availability."""
        self.dependencies = {
            'cairosvg': False,
            'reportlab': False,
            'svglib': False,
            'wand': False,
            'potrace': False  # For bitmap tracing
        }
        
        try:
            import cairosvg
            self.dependencies['cairosvg'] = True
            logger.info("CairoSVG available for SVG processing")
        except ImportError:
            logger.warning("CairoSVG not available - SVG conversion limited")
        
        try:
            import reportlab
            self.dependencies['reportlab'] = True
            logger.info("ReportLab available for PDF generation")
        except ImportError:
            logger.warning("ReportLab not available - PDF generation limited")
        
        try:
            import svglib
            self.dependencies['svglib'] = True
            logger.info("SVGLib available for SVG to PDF conversion")
        except ImportError:
            logger.warning("SVGLib not available - SVG to PDF conversion limited")
        
        try:
            from wand.image import Image as WandImage
            self.dependencies['wand'] = True
            logger.info("Wand/ImageMagick available for advanced conversions")
        except ImportError:
            logger.warning("Wand/ImageMagick not available - advanced conversions limited")
    
    def convert_to_vector(
        self, 
        input_path: str, 
        output_format: str = "svg",
        output_path: Optional[str] = None,
        trace_method: str = "potrace",
        optimize: bool = True
    ) -> str:
        """
        Convert a raster image to vector format.
        
        Args:
            input_path: Path to input raster image
            output_format: Target vector format ('svg', 'pdf', 'eps')
            output_path: Optional output path (auto-generated if None)
            trace_method: Tracing method to use
            optimize: Whether to optimize the output
            
        Returns:
            str: Path to the converted vector file
        """
        if not os.path.exists(input_path):
            raise VectorProcessingError(f"Input file not found: {input_path}")
        
        if output_format.lower() not in self.supported_vector_formats:
            raise VectorProcessingError(
                f"Unsupported vector format: {output_format}. "
                f"Supported: {', '.join(self.supported_vector_formats)}"
            )
        
        # Generate output path if not provided
        if not output_path:
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_filename = f"{base_name}_vector.{output_format.lower()}"
            output_path = os.path.join(Settings.GENERATED_IMAGES_DIR, output_filename)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            if output_format.lower() == "svg":
                return self._convert_to_svg(input_path, output_path, trace_method, optimize)
            elif output_format.lower() == "pdf":
                return self._convert_to_pdf(input_path, output_path, optimize)
            elif output_format.lower() == "eps":
                return self._convert_to_eps(input_path, output_path, optimize)
            
        except Exception as e:
            raise VectorProcessingError(f"Vector conversion failed: {str(e)}")
    
    def _convert_to_svg(
        self, 
        input_path: str, 
        output_path: str, 
        trace_method: str,
        optimize: bool
    ) -> str:
        """Convert image to SVG format."""
        logger.info(f"Converting {input_path} to SVG using {trace_method}")
        
        # Method 1: Try using potrace (if available)
        if trace_method == "potrace":
            try:
                return self._potrace_to_svg(input_path, output_path, optimize)
            except Exception as e:
                logger.warning(f"Potrace conversion failed: {e}")
        
        # Method 2: Simple SVG embedding (fallback)
        return self._embed_in_svg(input_path, output_path, optimize)
    
    def _convert_to_pdf(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert image to PDF format."""
        logger.info(f"Converting {input_path} to PDF")
        
        # Method 1: Try using ReportLab
        if self.dependencies['reportlab']:
            try:
                return self._reportlab_to_pdf(input_path, output_path, optimize)
            except Exception as e:
                logger.warning(f"ReportLab conversion failed: {e}")
        
        # Method 2: Try using Wand/ImageMagick
        if self.dependencies['wand']:
            try:
                return self._wand_to_pdf(input_path, output_path, optimize)
            except Exception as e:
                logger.warning(f"Wand conversion failed: {e}")
        
        # Method 3: Simple PIL to PDF
        return self._pil_to_pdf(input_path, output_path, optimize)
    
    def _convert_to_eps(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert image to EPS format."""
        logger.info(f"Converting {input_path} to EPS")
        
        # Try using Wand/ImageMagick for EPS
        if self.dependencies['wand']:
            try:
                return self._wand_to_eps(input_path, output_path, optimize)
            except Exception as e:
                logger.warning(f"Wand EPS conversion failed: {e}")
        
        # Fallback: PIL EPS (limited support)
        return self._pil_to_eps(input_path, output_path, optimize)
    
    def _potrace_to_svg(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert using potrace for true vector tracing."""
        import subprocess
        import tempfile
        
        # Convert to bitmap first if needed
        with tempfile.NamedTemporaryFile(suffix='.bmp', delete=False) as temp_bmp:
            try:
                # Convert to grayscale bitmap
                with Image.open(input_path) as img:
                    # Convert to grayscale and enhance contrast
                    gray_img = img.convert('L')
                    # Apply threshold to create clean bitmap
                    threshold = 128
                    gray_img = gray_img.point(lambda x: 0 if x < threshold else 255, '1')
                    gray_img.save(temp_bmp.name, 'BMP')
                
                # Run potrace
                cmd = ['potrace', '--svg', '-o', output_path, temp_bmp.name]
                if optimize:
                    cmd.extend(['--tight', '--smooth'])
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"Potrace conversion successful: {output_path}")
                    return output_path
                else:
                    raise Exception(f"Potrace failed: {result.stderr}")
                    
            finally:
                # Clean up temporary file
                if os.path.exists(temp_bmp.name):
                    os.unlink(temp_bmp.name)
    
    def _embed_in_svg(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Create SVG by embedding the raster image."""
        import base64
        
        # Read and encode the image
        with open(input_path, 'rb') as img_file:
            img_data = img_file.read()
            img_base64 = base64.b64encode(img_data).decode('utf-8')
        
        # Get image dimensions
        with Image.open(input_path) as img:
            width, height = img.size
        
        # Determine MIME type
        img_format = os.path.splitext(input_path)[1].lower()
        mime_type = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp'
        }.get(img_format, 'image/png')
        
        # Create SVG content
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg">
    <image x="0" y="0" width="{width}" height="{height}" href="data:{mime_type};base64,{img_base64}"/>
</svg>'''
        
        # Write SVG file
        with open(output_path, 'w', encoding='utf-8') as svg_file:
            svg_file.write(svg_content)
        
        logger.info(f"SVG embedding successful: {output_path}")
        return output_path
    
    def _reportlab_to_pdf(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert to PDF using ReportLab."""
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        from reportlab.lib.pagesizes import letter
        
        # Get image dimensions
        with Image.open(input_path) as img:
            img_width, img_height = img.size
        
        # Create PDF
        c = canvas.Canvas(output_path, pagesize=(img_width, img_height))
        c.drawImage(input_path, 0, 0, width=img_width, height=img_height)
        c.save()
        
        logger.info(f"ReportLab PDF conversion successful: {output_path}")
        return output_path
    
    def _wand_to_pdf(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert to PDF using Wand/ImageMagick."""
        from wand.image import Image as WandImage
        
        with WandImage(filename=input_path) as img:
            img.format = 'pdf'
            if optimize:
                img.compression_quality = Settings.PDF_QUALITY
            img.save(filename=output_path)
        
        logger.info(f"Wand PDF conversion successful: {output_path}")
        return output_path
    
    def _pil_to_pdf(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert to PDF using PIL."""
        with Image.open(input_path) as img:
            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            img.save(output_path, 'PDF', resolution=Settings.PDF_QUALITY)
        
        logger.info(f"PIL PDF conversion successful: {output_path}")
        return output_path
    
    def _wand_to_eps(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert to EPS using Wand/ImageMagick."""
        from wand.image import Image as WandImage
        
        with WandImage(filename=input_path) as img:
            img.format = 'eps'
            if optimize:
                img.compression_quality = Settings.PDF_QUALITY
            img.save(filename=output_path)
        
        logger.info(f"Wand EPS conversion successful: {output_path}")
        return output_path
    
    def _pil_to_eps(self, input_path: str, output_path: str, optimize: bool) -> str:
        """Convert to EPS using PIL (limited support)."""
        with Image.open(input_path) as img:
            # PIL's EPS support is limited, but we'll try
            img.save(output_path, 'EPS')
        
        logger.info(f"PIL EPS conversion successful: {output_path}")
        return output_path
    
    def optimize_svg(self, svg_path: str, output_path: Optional[str] = None) -> str:
        """
        Optimize an SVG file for smaller size and better performance.
        
        Args:
            svg_path: Path to input SVG file
            output_path: Optional output path
            
        Returns:
            str: Path to optimized SVG
        """
        if not Settings.SVG_OPTIMIZATION:
            return svg_path
        
        if not output_path:
            base_name = os.path.splitext(svg_path)[0]
            output_path = f"{base_name}_optimized.svg"
        
        try:
            # Basic SVG optimization - remove unnecessary whitespace and comments
            with open(svg_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Simple optimizations
            lines = content.split('\n')
            optimized_lines = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('<!--'):  # Remove comments and empty lines
                    optimized_lines.append(line)
            
            optimized_content = ' '.join(optimized_lines)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            
            logger.info(f"SVG optimization completed: {output_path}")
            return output_path
            
        except Exception as e:
            logger.warning(f"SVG optimization failed: {e}")
            return svg_path
    
    def get_vector_info(self, vector_path: str) -> Dict[str, Any]:
        """
        Get information about a vector file.
        
        Args:
            vector_path: Path to vector file
            
        Returns:
            dict: Vector file information
        """
        if not os.path.exists(vector_path):
            raise VectorProcessingError(f"Vector file not found: {vector_path}")
        
        info = {
            'path': vector_path,
            'format': os.path.splitext(vector_path)[1].lower().lstrip('.'),
            'size_bytes': os.path.getsize(vector_path),
            'exists': True
        }
        
        try:
            # Try to get dimensions if possible
            if info['format'] == 'svg':
                info.update(self._get_svg_info(vector_path))
            elif info['format'] == 'pdf':
                info.update(self._get_pdf_info(vector_path))
            
        except Exception as e:
            logger.warning(f"Could not get detailed vector info: {e}")
        
        return info
    
    def _get_svg_info(self, svg_path: str) -> Dict[str, Any]:
        """Get SVG-specific information."""
        import xml.etree.ElementTree as ET
        
        try:
            tree = ET.parse(svg_path)
            root = tree.getroot()
            
            info = {}
            
            # Get dimensions from viewBox or width/height attributes
            viewbox = root.get('viewBox')
            if viewbox:
                parts = viewbox.split()
                if len(parts) >= 4:
                    info['viewbox'] = viewbox
                    info['width'] = float(parts[2])
                    info['height'] = float(parts[3])
            
            width = root.get('width')
            height = root.get('height')
            if width and height:
                # Remove units if present
                width = ''.join(c for c in width if c.isdigit() or c == '.')
                height = ''.join(c for c in height if c.isdigit() or c == '.')
                if width and height:
                    info['declared_width'] = float(width)
                    info['declared_height'] = float(height)
            
            return info
            
        except Exception:
            return {}
    
    def _get_pdf_info(self, pdf_path: str) -> Dict[str, Any]:
        """Get PDF-specific information."""
        # This would require additional PDF parsing libraries
        # For now, return basic info
        return {'format_specific': 'pdf_info_not_implemented'}
    
    def test_vector_processing(self) -> bool:
        """
        Test vector processing capabilities.
        
        Returns:
            bool: True if basic vector processing works
        """
        try:
            # Create a simple test image
            test_img = Image.new('RGB', (100, 100), color='red')
            
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                test_img.save(temp_file.name, 'PNG')
                
                # Try converting to SVG
                svg_path = self.convert_to_vector(temp_file.name, 'svg')
                
                # Verify SVG exists
                success = os.path.exists(svg_path)
                
                # Clean up
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
                if os.path.exists(svg_path):
                    os.unlink(svg_path)
                
                return success
                
        except Exception as e:
            logger.error(f"Vector processing test failed: {e}")
            return False