"""
Package initialization for shared Image Agent components.
"""

# Version information
__version__ = "1.0.0"
__author__ = "Image Agent Team"

# Core components
try:
    from .src.file_manager import FileManager, TempFileManager, ConfigManager
    from .src.validation import InputValidator, SecurityValidator, ContentFilter
    from .src.image_encoder import ImageEncoder
    from .src.base_settings import BaseSettings, CommonPaths, LoggingConfig, ProviderConfig
    
    __all__ = [
        'FileManager',
        'TempFileManager', 
        'ConfigManager',
        'InputValidator',
        'SecurityValidator',
        'ContentFilter',
        'ImageEncoder',
        'BaseSettings',
        'CommonPaths',
        'LoggingConfig',
        'ProviderConfig',
    ]
    
except ImportError as e:
    # Handle import errors gracefully during development
    print(f"Warning: Failed to import some components: {e}")
    __all__ = []