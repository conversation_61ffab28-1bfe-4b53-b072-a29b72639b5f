<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Recraft Vector Graphics Agent{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --recraft-primary: #6366f1;
            --recraft-secondary: #8b5cf6;
            --recraft-accent: #06d6a0;
            --recraft-dark: #1e293b;
            --recraft-light: #f8fafc;
        }
        
        body {
            background: linear-gradient(135deg, var(--recraft-light) 0%, #e2e8f0 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--recraft-primary) !important;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, var(--recraft-primary), var(--recraft-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .btn-recraft {
            background: linear-gradient(135deg, var(--recraft-primary), var(--recraft-secondary));
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
        }
        
        .btn-recraft:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
            color: white;
        }
        
        .btn-outline-recraft {
            border: 2px solid var(--recraft-primary);
            color: var(--recraft-primary);
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 24px;
            background: transparent;
            transition: all 0.3s ease;
        }
        
        .btn-outline-recraft:hover {
            background: var(--recraft-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-ready { background-color: var(--recraft-accent); }
        .status-error { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
        
        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--recraft-primary), var(--recraft-secondary));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            margin-bottom: 16px;
        }
        
        .chat-container {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            background: white;
        }
        
        .message {
            margin-bottom: 16px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
        }
        
        .message.user {
            background: var(--recraft-primary);
            color: white;
            margin-left: auto;
        }
        
        .message.agent {
            background: #f1f5f9;
            color: var(--recraft-dark);
        }
        
        .vector-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .toast {
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .navbar {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9) !important;
            border-bottom: 1px solid rgba(99, 102, 241, 0.1);
        }
        
        .footer {
            background: var(--recraft-dark);
            color: var(--recraft-light);
            margin-top: 60px;
            padding: 40px 0 20px;
        }
        
        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-vector-square me-2"></i>
                Recraft Agent
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/vector"><i class="fas fa-bezier-curve me-1"></i>Vector Graphics</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/processing"><i class="fas fa-image me-1"></i>Image Processing</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/gallery"><i class="fas fa-images me-1"></i>Gallery</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="nav-link" id="status-indicator">
                            <span class="status-indicator status-warning pulse"></span>
                            Checking...
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-vector-square me-2"></i>Recraft Agent</h5>
                    <p class="mb-0">Professional vector graphics and image generation powered by AI</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="mb-2">
                        <a href="https://recraft.ai" target="_blank" class="text-light me-3">
                            <i class="fas fa-external-link-alt me-1"></i>Recraft API
                        </a>
                        <a href="https://github.com" target="_blank" class="text-light">
                            <i class="fab fa-github me-1"></i>Source Code
                        </a>
                    </div>
                    <small class="text-muted">Built with Flask, LangChain, and Recraft AI</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Global variables
        let sessionId = null;
        
        // Initialize session
        function initializeSession() {
            sessionId = generateSessionId();
        }
        
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        // Status checking
        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                const indicator = document.getElementById('status-indicator');
                if (data.ready) {
                    indicator.innerHTML = '<span class="status-indicator status-ready"></span>Ready';
                } else {
                    indicator.innerHTML = '<span class="status-indicator status-error"></span>Not Ready';
                }
            } catch (error) {
                console.error('Status check failed:', error);
                const indicator = document.getElementById('status-indicator');
                indicator.innerHTML = '<span class="status-indicator status-error"></span>Error';
            }
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastBody = toast.querySelector('.toast-body');
            const toastHeader = toast.querySelector('.toast-header');
            
            // Set message
            toastBody.textContent = message;
            
            // Set icon based on type
            const icon = toastHeader.querySelector('i');
            icon.className = `fas me-2`;
            switch (type) {
                case 'success':
                    icon.className += ` fa-check-circle text-success`;
                    break;
                case 'error':
                    icon.className += ` fa-exclamation-circle text-danger`;
                    break;
                case 'warning':
                    icon.className += ` fa-exclamation-triangle text-warning`;
                    break;
                default:
                    icon.className += ` fa-info-circle text-primary`;
            }
            
            // Show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        // Loading state management
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'block';
            }
        }
        
        function hideLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSession();
            checkStatus();
            
            // Check status every 30 seconds
            setInterval(checkStatus, 30000);
        });
        
        // Utility function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Error handling
        function handleApiError(error, context = 'Operation') {
            console.error(`${context} failed:`, error);
            showToast(`${context} failed: ${error.message || error}`, 'error');
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>