#!/usr/bin/env python3
"""
Test script to verify that prompt enhancement control is working correctly.
Tests both the automatic enhancement hiding and the manual control.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.responses_image_tool import ResponsesImageTool
from src.agent import ImageGenerationAgent
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_responses_tool_revised_prompt_control():
    """Test that the ResponsesImageTool respects the show_revised_prompt parameter."""
    print("🧪 Testing ResponsesImageTool revised prompt control...")
    
    try:
        tool = ResponsesImageTool()
        
        # Test 1: Default behavior (should not show revised prompt)
        print("\n📝 Test 1: Default behavior (show_revised_prompt=False)")
        
        # Mock a response object for testing
        class MockOutput:
            def __init__(self):
                self.type = "image_generation_call"
                self.result = "mock_base64_data"
                self.revised_prompt = "A beautiful, photorealistic sunset landscape with dramatic lighting and vibrant colors"
        
        class MockResponse:
            def __init__(self):
                self.id = "test_response_123"
                self.output = [MockOutput()]
        
        # Test with show_revised_prompt=False (default)
        result_false = tool._handle_standard_response(MockResponse(), "sunset landscape", show_revised_prompt=False)
        
        if "🔄 AI-optimized prompt:" in result_false:
            print("❌ FAILED: Revised prompt shown when show_revised_prompt=False")
            return False
        else:
            print("✅ PASSED: Revised prompt hidden when show_revised_prompt=False")
        
        # Test 2: Explicit show (should show revised prompt)
        print("\n📝 Test 2: Explicit show (show_revised_prompt=True)")
        result_true = tool._handle_standard_response(MockResponse(), "sunset landscape", show_revised_prompt=True)
        
        if "🔄 AI-optimized prompt:" in result_true:
            print("✅ PASSED: Revised prompt shown when show_revised_prompt=True")
        else:
            print("❌ FAILED: Revised prompt not shown when show_revised_prompt=True")
            return False
        
        print("\n✅ All ResponsesImageTool tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ResponsesImageTool test failed: {str(e)}")
        return False

def test_agent_system_prompt():
    """Test that the agent system prompt includes the new guidelines."""
    print("\n🧪 Testing Agent system prompt updates...")
    
    try:
        agent = ImageGenerationAgent()
        system_prompt = agent._get_current_system_prompt()
        
        # Check for the new guideline
        if "show_revised_prompt parameter" in system_prompt:
            print("✅ PASSED: Agent system prompt includes show_revised_prompt guideline")
        else:
            print("❌ FAILED: Agent system prompt missing show_revised_prompt guideline")
            return False
        
        # Check for the new example
        if "show_revised_prompt=true" in system_prompt:
            print("✅ PASSED: Agent system prompt includes show_revised_prompt example")
        else:
            print("❌ FAILED: Agent system prompt missing show_revised_prompt example")
            return False
        
        print("✅ All Agent system prompt tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Agent system prompt test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Prompt Enhancement Control Tests...")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: ResponsesImageTool revised prompt control
    if not test_responses_tool_revised_prompt_control():
        all_tests_passed = False
    
    # Test 2: Agent system prompt updates
    if not test_agent_system_prompt():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED! Prompt enhancement control is working correctly.")
        print("\n📋 Summary of changes:")
        print("✅ Automatic prompt enhancement display is now disabled by default")
        print("✅ Users can enable it via 'Show AI-Optimized Prompt' control in UI")
        print("✅ Agent understands show_revised_prompt parameter")
        print("✅ ResponsesImageTool respects the show_revised_prompt setting")
        return True
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
