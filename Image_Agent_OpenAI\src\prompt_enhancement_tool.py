"""
Langchain tool for enhancing user prompts using AI.
Improves prompt quality, specificity, and effectiveness for image generation.
"""
from typing import Optional, Type, Dict, Any
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from config.settings import Settings
import logging
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PromptEnhancementInput(BaseModel):
    """Input schema for the prompt enhancement tool."""
    original_prompt: str = Field(description="The original user prompt to enhance")
    context: Optional[str] = Field(
        default=None,
        description="Additional context about the image generation task"
    )
    style_preference: Optional[str] = Field(
        default=None,
        description="Preferred artistic style or approach"
    )
    enhancement_level: Optional[str] = Field(
        default="moderate",
        description="Level of enhancement: 'light', 'moderate', 'extensive'"
    )

class PromptEnhancementTool(BaseTool):
    """Tool for enhancing user prompts for better image generation results."""

    name: str = "PromptEnhancementTool"
    description: str = """Enhance and improve user prompts for image generation.

    This tool takes a user's original prompt and enhances it with:
    - More specific and descriptive language
    - Better artistic and technical terminology
    - Improved composition and lighting descriptions
    - Enhanced style and mood specifications
    - Optimized structure for AI image generation

    Use this tool when users want to improve their prompts or when you detect
    that a prompt could benefit from enhancement for better results."""

    args_schema: Type[BaseModel] = PromptEnhancementInput
    llm: Optional[ChatOpenAI] = Field(default=None, exclude=True)

    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True

    def __init__(self, **kwargs):
        """Initialize the prompt enhancement tool."""
        super().__init__(**kwargs)
        self.llm = ChatOpenAI(
            model=Settings.PROMPT_ENHANCEMENT_MODEL,
            openai_api_key=Settings.OPENAI_API_KEY,
            temperature=0.7,
            max_tokens=Settings.PROMPT_ENHANCEMENT_MAX_TOKENS
        )
        logger.info("Initialized PromptEnhancementTool")
    
    def _run(
        self,
        original_prompt: str,
        context: Optional[str] = None,
        style_preference: Optional[str] = None,
        enhancement_level: str = "moderate",
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Enhance the given prompt using AI.
        
        Args:
            original_prompt: The original user prompt
            context: Additional context about the task
            style_preference: Preferred artistic style
            enhancement_level: Level of enhancement to apply
            run_manager: Callback manager for the tool run
            
        Returns:
            str: JSON string containing the enhanced prompt and metadata
        """
        try:
            logger.info(f"Enhancing prompt: '{original_prompt[:50]}...'")
            
            # Create the enhancement system prompt
            system_prompt = self._create_enhancement_system_prompt(enhancement_level)
            
            # Create the user message with the prompt to enhance
            user_message = self._create_enhancement_user_message(
                original_prompt, context, style_preference
            )
            
            # Get the enhanced prompt from the LLM
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_message)
            ]
            
            response = self.llm.invoke(messages)
            enhanced_content = response.content.strip()
            
            # Parse the response and create the result
            result = self._parse_enhancement_response(enhanced_content, original_prompt)
            
            logger.info("Successfully enhanced prompt")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            error_msg = f"Error enhancing prompt: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                "success": False,
                "error": error_msg,
                "original_prompt": original_prompt,
                "enhanced_prompt": original_prompt  # Fallback to original
            })
    
    def _create_enhancement_system_prompt(self, enhancement_level: str) -> str:
        """Create the system prompt for enhancement based on the level."""
        base_prompt = """You are an expert prompt engineer specializing in AI image generation. Your task is to enhance user prompts to produce better, more detailed, and more visually appealing images.

ENHANCEMENT PRINCIPLES:
1. Preserve the user's core intent and vision
2. Add specific, descriptive details that improve visual quality
3. Include technical photography/art terms when appropriate
4. Enhance composition, lighting, and mood descriptions
5. Maintain natural, readable language
6. Optimize for AI image generation models

RESPONSE FORMAT:
Provide your response as a JSON object with these fields:
{
  "enhanced_prompt": "The improved prompt text",
  "improvements": ["List of specific improvements made"],
  "reasoning": "Brief explanation of enhancement strategy",
  "confidence": 0.85
}"""

        level_instructions = {
            "light": "\nENHANCEMENT LEVEL: LIGHT - Make minimal improvements, focus on clarity and basic details.",
            "moderate": "\nENHANCEMENT LEVEL: MODERATE - Add meaningful details, improve composition and style descriptions.",
            "extensive": "\nENHANCEMENT LEVEL: EXTENSIVE - Significantly expand the prompt with rich details, technical terms, and artistic specifications."
        }
        
        return base_prompt + level_instructions.get(enhancement_level, level_instructions["moderate"])
    
    def _create_enhancement_user_message(
        self, 
        original_prompt: str, 
        context: Optional[str], 
        style_preference: Optional[str]
    ) -> str:
        """Create the user message for the enhancement request."""
        message = f"Please enhance this image generation prompt:\n\nORIGINAL PROMPT: {original_prompt}"
        
        if context:
            message += f"\n\nCONTEXT: {context}"
        
        if style_preference:
            message += f"\n\nSTYLE PREFERENCE: {style_preference}"
        
        message += "\n\nPlease provide the enhanced prompt following the JSON format specified."
        
        return message
    
    def _parse_enhancement_response(self, response_content: str, original_prompt: str) -> Dict[str, Any]:
        """Parse the LLM response and create a structured result."""
        try:
            # Try to parse as JSON first
            if response_content.strip().startswith('{'):
                result = json.loads(response_content)
                result["success"] = True
                result["original_prompt"] = original_prompt
                return result
            
            # If not JSON, treat as plain text enhanced prompt
            return {
                "success": True,
                "original_prompt": original_prompt,
                "enhanced_prompt": response_content,
                "improvements": ["Enhanced with AI assistance"],
                "reasoning": "Prompt improved for better image generation results",
                "confidence": 0.8
            }
            
        except json.JSONDecodeError:
            # Fallback: treat entire response as enhanced prompt
            return {
                "success": True,
                "original_prompt": original_prompt,
                "enhanced_prompt": response_content,
                "improvements": ["Enhanced with AI assistance"],
                "reasoning": "Prompt improved for better image generation results",
                "confidence": 0.7
            }
    
    def test_enhancement(self) -> bool:
        """Test the prompt enhancement functionality."""
        try:
            test_prompt = "a cat sitting on a chair"
            result = self._run(test_prompt)
            
            # Parse the result to check if it's valid
            parsed_result = json.loads(result)
            
            if parsed_result.get("success") and parsed_result.get("enhanced_prompt"):
                logger.info("Prompt enhancement test passed")
                return True
            else:
                logger.error("Prompt enhancement test failed: Invalid result structure")
                return False
                
        except Exception as e:
            logger.error(f"Prompt enhancement test failed: {str(e)}")
            return False

# Test function for standalone usage
def test_prompt_enhancement_tool():
    """Test the prompt enhancement tool."""
    try:
        if not Settings.PROMPT_ENHANCEMENT_ENABLED:
            print("⚠️  Prompt enhancement is disabled in settings")
            return False
            
        tool = PromptEnhancementTool()
        
        if tool.test_enhancement():
            print("✓ Prompt enhancement tool test passed")
            return True
        else:
            print("✗ Prompt enhancement tool test failed")
            return False
    except Exception as e:
        print(f"✗ Prompt enhancement tool test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_prompt_enhancement_tool()
