"""
Test script to verify the Recraft agent installation and basic functionality.
Run this script to test the system before deploying.
"""
import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Core dependencies
        import langchain
        print("✓ LangChain imported successfully")
        
        import openai
        print("✓ OpenAI imported successfully")
        
        import requests
        print("✓ Requests imported successfully")
        
        # Image processing
        from PIL import Image
        print("✓ PIL imported successfully")
        
        # Vector processing
        import cairosvg
        print("✓ CairoSVG imported successfully")
        
        from reportlab.pdfgen import canvas
        print("✓ ReportLab imported successfully")
        
        # Flask for web interface
        import flask
        print("✓ Flask imported successfully")
        
        # Our modules
        from config.settings import Settings
        print("✓ Settings module imported successfully")
        
        from src.recraft_tool import RecraftClient
        print("✓ Recraft client imported successfully")
        
        from src.agent import RecraftAgent
        print("✓ Recraft agent imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


def test_environment():
    """Test environment configuration."""
    print("\nTesting environment configuration...")
    
    # Check API keys
    recraft_key = os.getenv('RECRAFT_API_KEY')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    if recraft_key:
        print("✓ Recraft API key found")
    else:
        print("✗ Recraft API key not found in environment")
        return False
        
    if openai_key:
        print("✓ OpenAI API key found")
    else:
        print("✗ OpenAI API key not found in environment")
        return False
    
    # Test settings validation
    try:
        if Settings.validate():
            print("✓ Settings validation passed")
            return True
        else:
            print("✗ Settings validation failed")
            return False
    except Exception as e:
        print(f"✗ Settings validation error: {e}")
        return False


def test_recraft_api():
    """Test basic Recraft API connectivity."""
    print("\nTesting Recraft API connectivity...")
    
    try:
        from src.recraft_tool import RecraftClient
        client = RecraftClient()
        
        # Test if we can make a simple request (this won't actually generate)
        if hasattr(client, '_test_connection'):
            result = client._test_connection()
            if result:
                print("✓ Recraft API connection test passed")
                return True
            else:
                print("✗ Recraft API connection test failed")
                return False
        else:
            print("⚠ Recraft API connection test not implemented")
            return True
            
    except Exception as e:
        print(f"✗ Recraft API test error: {e}")
        return False


def test_agent_initialization():
    """Test agent initialization."""
    print("\nTesting agent initialization...")
    
    try:
        from src.agent import RecraftAgent
        agent = RecraftAgent()
        
        print("✓ Recraft agent initialized successfully")
        
        # Test capabilities
        capabilities = agent.get_capabilities()
        print(f"✓ Agent capabilities: {list(capabilities.get('capabilities', {}).keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent initialization error: {e}")
        return False


def test_vector_processing():
    """Test vector processing capabilities."""
    print("\nTesting vector processing...")
    
    try:
        from src.vector_processor import VectorProcessor
        processor = VectorProcessor()
        
        # Test basic functionality without actual files
        print("✓ Vector processor initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Vector processing test error: {e}")
        return False


def test_web_app():
    """Test Flask web application."""
    print("\nTesting web application...")
    
    try:
        import web_app
        app = web_app.app
        
        with app.test_client() as client:
            # Test health endpoint
            response = client.get('/health')
            if response.status_code == 200:
                print("✓ Health endpoint working")
            else:
                print(f"✗ Health endpoint failed: {response.status_code}")
                return False
                
            # Test API status
            response = client.get('/api/status')
            if response.status_code == 200:
                print("✓ API status endpoint working")
            else:
                print(f"⚠ API status endpoint: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"✗ Web app test error: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("🧪 RECRAFT AGENT SYSTEM TESTS")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Environment Test", test_environment),
        ("Recraft API Test", test_recraft_api),
        ("Agent Initialization Test", test_agent_initialization),
        ("Vector Processing Test", test_vector_processing),
        ("Web Application Test", test_web_app),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The Recraft agent is ready to use.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)