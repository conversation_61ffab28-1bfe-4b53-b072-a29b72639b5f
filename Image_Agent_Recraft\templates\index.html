{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Hero Section -->
        <div class="card mb-5 text-center">
            <div class="card-body py-5">
                <div class="mb-4">
                    <i class="fas fa-vector-square gradient-text" style="font-size: 4rem;"></i>
                </div>
                <h1 class="display-4 gradient-text mb-3">Recraft Vector Graphics Agent</h1>
                <p class="lead text-muted mb-4">
                    Professional AI-powered vector graphics generation and image processing
                </p>
                
                <div class="row justify-content-center mb-4">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            {% if is_ready %}
                                <span class="status-indicator status-ready"></span>
                                <span class="text-success fw-semibold">System Ready</span>
                            {% else %}
                                <span class="status-indicator status-error"></span>
                                <span class="text-danger fw-semibold">System Not Ready</span>
                            {% endif %}
                        </div>
                        
                        {% if not is_ready %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Please check your configuration and API keys in the .env file
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="#quick-generate" class="btn btn-recraft">
                        <i class="fas fa-magic me-2"></i>Quick Generate
                    </a>
                    <a href="/vector" class="btn btn-outline-recraft">
                        <i class="fas fa-bezier-curve me-2"></i>Vector Graphics
                    </a>
                    <a href="/processing" class="btn btn-outline-recraft">
                        <i class="fas fa-image me-2"></i>Image Processing
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">Key Features</h2>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-vector-square"></i>
                </div>
                <h4>Vector Graphics</h4>
                <p class="text-muted">Generate scalable SVG and PDF graphics perfect for logos, icons, and professional designs.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-palette"></i>
                </div>
                <h4>Professional Styles</h4>
                <p class="text-muted">Multiple artistic styles and substyles for brand-consistent, professional-quality outputs.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-cogs"></i>
                </div>
                <h4>Image Processing</h4>
                <p class="text-muted">Advanced image processing, enhancement, background removal, and format conversion.</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Generate Section -->
<div class="row" id="quick-generate">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-magic me-2"></i>Quick Generate
                </h3>
            </div>
            <div class="card-body">
                <form id="quick-generate-form">
                    <div class="row">
                        <div class="col-lg-8 mb-3">
                            <label for="prompt" class="form-label">Describe what you want to create:</label>
                            <textarea 
                                class="form-control" 
                                id="prompt" 
                                name="prompt" 
                                rows="3" 
                                placeholder="e.g., A modern logo for a tech company with blue and white colors, minimalist design"
                                required
                            ></textarea>
                        </div>
                        <div class="col-lg-4 mb-3">
                            <label for="output-type" class="form-label">Output Type:</label>
                            <select class="form-select" id="output-type">
                                <option value="standard">Standard Image</option>
                                <option value="vector">Vector Graphics</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="style" class="form-label">Style (Optional):</label>
                            <select class="form-select" id="style">
                                <option value="">Auto-select</option>
                                <option value="realistic_image">Realistic Image</option>
                                <option value="digital_illustration">Digital Illustration</option>
                                <option value="vector_illustration">Vector Illustration</option>
                                <option value="icon">Icon</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="size" class="form-label">Size:</label>
                            <select class="form-select" id="size">
                                <option value="1024x1024">Square (1024x1024)</option>
                                <option value="1365x1024">Landscape (1365x1024)</option>
                                <option value="1024x1365">Portrait (1024x1365)</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-recraft w-100" id="generate-btn">
                                <i class="fas fa-magic me-2"></i>Generate
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- Loading Animation -->
                <div class="loading-spinner" id="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Generating...</span>
                    </div>
                    <p class="mt-2 text-muted">Creating your image...</p>
                </div>
                
                <!-- Results -->
                <div id="generation-result" style="display: none;">
                    <hr class="my-4">
                    <h5>Generation Result:</h5>
                    <div id="result-content" class="p-3 bg-light rounded">
                        <!-- Result will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chat Interface -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Chat with Recraft Agent
                </h3>
                <button class="btn btn-outline-secondary btn-sm" onclick="clearChat()">
                    <i class="fas fa-trash me-1"></i>Clear Chat
                </button>
            </div>
            <div class="card-body">
                <div class="chat-container" id="chat-container">
                    <div class="text-center text-muted">
                        <i class="fas fa-robot fa-2x mb-3"></i>
                        <p>Start a conversation with the Recraft agent. Ask about image generation, vector graphics, or image processing!</p>
                    </div>
                </div>
                
                <div class="input-group mt-3">
                    <input 
                        type="text" 
                        class="form-control" 
                        id="chat-input" 
                        placeholder="Type your message..."
                        onkeypress="handleChatKeyPress(event)"
                    >
                    <button class="btn btn-recraft" onclick="sendChatMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Capabilities Info -->
{% if capabilities %}
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>System Capabilities
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Available Tools:</h5>
                        <ul class="list-unstyled">
                            {% for tool in capabilities.tools %}
                            <li><i class="fas fa-check-circle text-success me-2"></i>{{ tool }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Supported Formats:</h5>
                        {% if capabilities.supported_formats %}
                            <div class="mb-2">
                                <strong>Input:</strong> 
                                {{ capabilities.supported_formats.input | join(', ') if capabilities.supported_formats.input }}
                            </div>
                            <div>
                                <strong>Vector Output:</strong> 
                                {{ capabilities.supported_formats.vector_output | join(', ') if capabilities.supported_formats.vector_output }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// Quick generation handling
document.getElementById('quick-generate-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const prompt = document.getElementById('prompt').value.trim();
    if (!prompt) {
        showToast('Please enter a description', 'warning');
        return;
    }
    
    const outputType = document.getElementById('output-type').value;
    const style = document.getElementById('style').value;
    const size = document.getElementById('size').value;
    
    // Show loading
    document.getElementById('generate-btn').disabled = true;
    showLoading('loading-spinner');
    hideResult();
    
    try {
        const options = {
            vector_output: outputType === 'vector',
            size: size
        };
        
        if (style) {
            options.style = style;
        }
        
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: prompt,
                options: options,
                session_id: sessionId
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showResult(data.response);
            showToast('Image generated successfully!', 'success');
        } else {
            throw new Error(data.error || 'Generation failed');
        }
        
    } catch (error) {
        handleApiError(error, 'Image generation');
    } finally {
        document.getElementById('generate-btn').disabled = false;
        hideLoading('loading-spinner');
    }
});

function showResult(content) {
    document.getElementById('result-content').innerHTML = content;
    document.getElementById('generation-result').style.display = 'block';
    document.getElementById('generation-result').scrollIntoView({ behavior: 'smooth' });
}

function hideResult() {
    document.getElementById('generation-result').style.display = 'none';
}

// Chat functionality
let chatHistory = [];

function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

async function sendChatMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat(message, 'user');
    input.value = '';
    
    // Show typing indicator
    addTypingIndicator();
    
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                session_id: sessionId
            })
        });
        
        const data = await response.json();
        
        removeTypingIndicator();
        
        if (data.status === 'success') {
            addMessageToChat(data.response, 'agent');
        } else {
            throw new Error(data.error || 'Chat failed');
        }
        
    } catch (error) {
        removeTypingIndicator();
        addMessageToChat('Sorry, I encountered an error processing your message.', 'agent');
        handleApiError(error, 'Chat');
    }
}

function addMessageToChat(message, sender) {
    const chatContainer = document.getElementById('chat-container');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender} fade-in`;
    messageDiv.textContent = message;
    
    chatContainer.appendChild(messageDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
    
    // Store in history
    chatHistory.push({ sender, message });
}

function addTypingIndicator() {
    const chatContainer = document.getElementById('chat-container');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message agent typing-indicator';
    typingDiv.innerHTML = '<i class="fas fa-circle pulse me-1"></i><i class="fas fa-circle pulse me-1" style="animation-delay: 0.2s;"></i><i class="fas fa-circle pulse" style="animation-delay: 0.4s;"></i>';
    typingDiv.id = 'typing-indicator';
    
    chatContainer.appendChild(typingDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function removeTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    if (indicator) {
        indicator.remove();
    }
}

function clearChat() {
    document.getElementById('chat-container').innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-robot fa-2x mb-3"></i>
            <p>Start a conversation with the Recraft agent. Ask about image generation, vector graphics, or image processing!</p>
        </div>
    `;
    chatHistory = [];
    showToast('Chat cleared', 'info');
}
</script>
{% endblock %}