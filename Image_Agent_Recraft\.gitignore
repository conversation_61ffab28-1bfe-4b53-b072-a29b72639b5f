# Environment files
.env
.env.local
.env.production
.env.staging

# Virtual environment
venv/
env/
.venv/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Generated images (keep a few samples but ignore bulk generations)
generated_images/*.png
generated_images/*.jpg
generated_images/*.jpeg
generated_images/*.svg
generated_images/*.pdf
!generated_images/sample_*
!generated_images/README.md

# Log files
*.log
logs/

# VS Code
.vscode/
.history/

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Flask session files
flask_session/

# Docker
.dockerignore

# API Keys and secrets
api_keys.txt
secrets.json
*.key
*.pem

# Local development files
local_config.py
test_images/
temp_images/