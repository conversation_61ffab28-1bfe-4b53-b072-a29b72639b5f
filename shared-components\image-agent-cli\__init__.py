"""
Package initialization for Image Agent CLI framework.
"""

# Version information
__version__ = "1.0.0"
__author__ = "Image Agent Team"

# CLI components
try:
    from .src.base_cli import BaseCLI, AsyncBaseCL<PERSON>, create_cli_entry_point
    
    __all__ = [
        'BaseCLI',
        'AsyncBaseCLI',
        'create_cli_entry_point',
    ]
    
except ImportError as e:
    # Handle import errors gracefully during development
    print(f"Warning: Failed to import some CLI components: {e}")
    __all__ = []