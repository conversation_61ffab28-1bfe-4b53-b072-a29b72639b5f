# AI Agent Instructions for Image Generation Agent

## Project Overview
This is a sophisticated AI image generation system built with LangChain, featuring multi-turn conversations, reference image support, and advanced AI tool orchestration. The system has both CLI and Flask web interfaces, uses OpenAI's Responses API with fallback to legacy DALL-E tools, and implements a complex agent architecture.

## Architecture & Key Components

### Core Agent Architecture (`src/agent.py`)
- **Multi-Tool System**: Primary ResponsesImageTool with legacy OpenAIImageGenerator/Editor fallbacks
- **Dynamic Tool Selection**: User-controlled API mode switching between advanced and legacy tools  
- **Custom Ruleset Support**: Runtime agent behavior modification via system prompt injection
- **Tool Priority Logic**: ResponsesImageTool preferred for multi-turn, streaming, and reference image workflows

### Tool Hierarchy & Selection Patterns
```python
# Primary tool for advanced features
ResponsesImageTool(continue_previous=True, reference_images=[...])  

# Legacy tools for direct control
OpenAIImageGenerator(prompt="exact prompt")  # No AI enhancement
OpenAIImageEditor(image=base64_data, prompt="edit instruction")
```

### Multi-Turn Conversation Flow
The agent automatically detects continuation patterns (`continue`, `make it`, `edit previous`) and maintains context through `previous_response_id` tracking in ResponsesImageTool. Each generation stores response metadata for iterative editing.

## Development Patterns

### Testing Strategy
- **Mock-Heavy Architecture**: All tests use `@patch` decorators for external dependencies (LLM, OpenAI tools)
- **Tool Isolation Testing**: Each tool tested independently with mocked API calls
- **API Endpoint Testing**: Flask test client for web interface validation
- **Custom Ruleset Validation**: Security checks for prompt injection prevention

Example test pattern:
```python
@patch('src.agent.LLMIntegration')
@patch('src.agent.ResponsesImageTool') 
def test_feature(self, mock_tool, mock_llm):
    # Mock setup, then test agent behavior
```

### Configuration Management (`config/settings.py`)
Environment-driven configuration with validation. All OpenAI API settings, model choices, and feature flags centralized here. Settings class provides validation methods that must pass before agent initialization.

### Image Processing Pipeline (`utils/image_encoder.py`)
- **Upload Processing**: Validates format/size, creates temporary files, base64 encoding
- **Reference Image Workflow**: File path validation, format conversion for API consumption
- **Cleanup Management**: Automatic temporary file cleanup after processing

## Critical Workflows

### CLI Interactive Mode (`main.py`)
Advanced command system with context-aware responses:
- `refs path1 path2` - Load reference images with validation feedback
- `continue instruction` - Multi-turn editing with automatic context detection
- `stream prompt` - Streaming generation with partial image display
- `history` - Generation history with response ID tracking

### Web Interface Patterns (`web_app.py`)
- **Message Enhancement**: Automatic API mode instruction injection before agent invocation
- **Image Upload Processing**: Multi-file upload with error aggregation and cleanup
- **Session Management**: Custom ruleset storage per IP address (simple session tracking)
- **Parameter Handling**: JSON parameter passing with message augmentation

### Docker Deployment
Production-ready containerization with health checks, volume mounts for persistent image storage, and proper environment variable handling. Uses Gunicorn for production WSGI serving.

## Integration Points

### OpenAI API Integration
- **Responses API**: Primary integration for advanced image generation with conversation tracking
- **Legacy API**: Fallback tools for direct prompt control without AI enhancement
- **Model Configuration**: gpt-4o-mini for reasoning, gpt-image-1 for generation

### LangChain Tool Architecture
Tools implement BaseTool with Pydantic input schemas. Agent uses `create_openai_tools_agent` with tool binding and execution through AgentExecutor with error handling and retry logic.

### File System Conventions
- `generated_images/` - Auto-created storage for all generated images with timestamp naming
- `config/` - Centralized configuration with environment variable loading
- `src/` - Core agent and tool implementations
- `utils/` - Shared utilities (image processing, encoding)
- `templates/` and `static/` - Web interface assets

## Debugging & Development

### Agent Behavior Analysis
Enable verbose logging in AgentExecutor to trace tool selection and execution. The agent's system prompt contains extensive tool selection logic that can be inspected by examining `_get_default_system_prompt()`.

### Tool Testing
Each tool has a `test_*` method for connectivity/functionality verification. Run comprehensive tests via `python main.py test` for full system validation.

### Web Interface Development
Flask app with CORS enabled, uses `render_template` for HTML serving and JSON API endpoints under `/api/`. Development mode available via direct `python web_app.py` execution.

## Project-Specific Conventions

- **Error Handling**: All tool methods return string responses, errors included in response text rather than exceptions
- **Image Storage**: Automatic timestamped filenames, no user-specified naming
- **Tool Selection**: User message enhancement pattern for API mode control rather than direct tool specification
- **Configuration Validation**: Settings validation required before any agent initialization
- **Session Persistence**: Simple IP-based session tracking for web interface, no database required

This codebase prioritizes user experience through intelligent tool orchestration while maintaining fallback compatibility and comprehensive testing coverage.