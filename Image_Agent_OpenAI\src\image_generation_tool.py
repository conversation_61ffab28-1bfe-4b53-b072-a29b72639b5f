"""
Custom Langchain tool for image generation using OpenAI's gpt-image-1 model.
"""
from typing import Optional, Type
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
import openai
from config.settings import Settings
import logging
import requests
from io import BytesIO
import base64
import os
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageGenerationInput(BaseModel):
    """Input schema for the image generation tool."""
    prompt: str = Field(description="Detailed text prompt describing the image to generate (max 32000 characters)")
    size: Optional[str] = Field(
        default=Settings.DEFAULT_IMAGE_SIZE,
        description="Size of the image ('auto', '1024x1024', '1536x1024', '1024x1536')"
    )
    quality: Optional[str] = Field(
        default=Settings.DEFAULT_IMAGE_QUALITY,
        description="Quality of the image ('auto', 'high', 'medium', 'low')"
    )
    output_format: Optional[str] = Field(
        default=Settings.DEFAULT_OUTPUT_FORMAT,
        description="Output format ('png', 'jpeg', or 'webp')"
    )
    background: Optional[str] = Field(
        default=Settings.DEFAULT_BACKGROUND,
        description="Background setting ('auto', 'transparent', 'opaque'). Transparent only works with png/webp"
    )
    moderation: Optional[str] = Field(
        default=Settings.DEFAULT_MODERATION,
        description="Content moderation level ('auto' for standard, 'low' for less restrictive)"
    )
    output_compression: Optional[int] = Field(
        default=Settings.DEFAULT_OUTPUT_COMPRESSION,
        description="Compression level 0-100% for jpeg/webp formats"
    )

class OpenAIImageGenerator(BaseTool):
    """Custom Langchain tool for generating images using OpenAI's gpt-image-1 model."""
    
    name: str = "OpenAIImageGenerator"
    description: str = (
        "Tool to generate images using OpenAI's gpt-image-1 model. "
        "Input should be a detailed text prompt describing the image. "
        "The tool will return a URL to the generated image."
    )
    args_schema: Type[BaseModel] = ImageGenerationInput
    
    def __init__(self):
        """Initialize the image generation tool."""
        super().__init__()
        logger.info(f"Initialized OpenAI Image Generator with model: {Settings.IMAGE_MODEL}")
    
    def _get_client(self):
        """Get the OpenAI client instance."""
        return openai.OpenAI(api_key=Settings.OPENAI_API_KEY)
    
    def _run(
        self,
        prompt: str,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        output_format: Optional[str] = None,
        background: Optional[str] = None,
        moderation: Optional[str] = None,
        output_compression: Optional[int] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Execute the image generation tool.
        
        Args:
            prompt: Text prompt describing the image
            size: Image size (optional)
            quality: Image quality (optional)
            output_format: Output format (optional)
            background: Background setting (optional)
            moderation: Moderation level (optional)
            output_compression: Compression level (optional)
            run_manager: Callback manager (optional)
            
        Returns:
            str: Base64 encoded image data with metadata or error message
        """
        try:
            # Use defaults if not provided
            size = size or Settings.DEFAULT_IMAGE_SIZE
            quality = quality or Settings.DEFAULT_IMAGE_QUALITY
            output_format = output_format or Settings.DEFAULT_OUTPUT_FORMAT
            background = background or Settings.DEFAULT_BACKGROUND
            moderation = moderation or Settings.DEFAULT_MODERATION
            output_compression = output_compression or Settings.DEFAULT_OUTPUT_COMPRESSION
            
            logger.info(f"Generating image with prompt: '{prompt[:50]}...'")
            logger.info(f"Parameters - Size: {size}, Quality: {quality}, Format: {output_format}")
            logger.info(f"Background: {background}, Moderation: {moderation}, Compression: {output_compression}%")
            
            # Build API parameters
            api_params = {
                "model": Settings.IMAGE_MODEL,
                "prompt": prompt,
                "size": size,
                "quality": quality,
                "output_format": output_format,
                "background": background,
                "moderation": moderation,
                "n": 1
            }
            
            # Add compression only for jpeg and webp formats
            if output_format in ["jpeg", "webp"] and output_compression != 100:
                api_params["output_compression"] = output_compression
            
            # Get client and make API call to gpt-image-1
            client = self._get_client()
            response = client.images.generate(**api_params)
            
            # gpt-image-1 returns base64-encoded images
            image_b64 = response.data[0].b64_json
            
            # Get token usage information if available
            usage_info = ""
            if hasattr(response, 'usage') and response.usage:
                usage = response.usage
                usage_info = f"\nToken Usage - Total: {usage.total_tokens}, Input: {usage.input_tokens}, Output: {usage.output_tokens}"
            
            logger.info(f"Successfully generated image with gpt-image-1{usage_info}")
            
            # Auto-save the image to avoid base64 display issues
            try:
                import os
                from datetime import datetime
                
                # Create directory
                os.makedirs("generated_images", exist_ok=True)
                
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_image_{timestamp}.{output_format}"
                filepath = os.path.join("generated_images", filename)
                
                # Save the image
                image_bytes = base64.b64decode(image_b64)
                with open(filepath, 'wb') as f:
                    f.write(image_bytes)
                
                abs_path = os.path.abspath(filepath)
                
                result_msg = f"""Image generated successfully with gpt-image-1!
✓ Format: {output_format.upper()}
✓ Size: {size}
✓ Quality: {quality}
✓ Background: {background}
✓ Auto-saved to: {abs_path}
✓ File size: {len(image_bytes):,} bytes ({len(image_bytes)/1024/1024:.2f} MB){usage_info}

The image has been automatically saved and is ready to use!"""
                
            except Exception as save_error:
                logger.error(f"Failed to auto-save image: {str(save_error)}")
                result_msg = f"""Image generated successfully with gpt-image-1!
✓ Format: {output_format.upper()}
✓ Size: {size}
✓ Quality: {quality}
✓ Background: {background}
✓ Base64 length: {len(image_b64)} characters{usage_info}

⚠️ Auto-save failed: {str(save_error)}
Note: Base64 data is too long to display in terminal (1.8+ million characters)."""
            
            return result_msg
            
        except openai.OpenAIError as e:
            error_msg = f"OpenAI API error: {str(e)}"
            logger.error(error_msg)
            return f"Error generating image: {error_msg}"
        
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return f"Error generating image: {error_msg}"
    
    async def _arun(
        self,
        prompt: str,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        output_format: Optional[str] = None,
        background: Optional[str] = None,
        moderation: Optional[str] = None,
        output_compression: Optional[int] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Async version of the tool execution.
        For now, we'll use the sync version.
        """
        return self._run(prompt, size, quality, output_format, background, moderation, output_compression, run_manager)
    
    def test_generation(self, test_prompt: str = "A simple red circle on a white background") -> bool:
        """
        Test the image generation functionality.
        
        Args:
            test_prompt: Simple prompt for testing
            
        Returns:
            bool: True if test successful, False otherwise
        """
        try:
            result = self._run(test_prompt)
            if "Error" not in result:
                logger.info("Image generation test passed")
                return True
            else:
                logger.error(f"Image generation test failed: {result}")
                return False
        except Exception as e:
            logger.error(f"Image generation test failed with exception: {str(e)}")
            return False

# Test function for standalone usage
def test_image_generation_tool():
    """Test the image generation tool."""
    try:
        tool = OpenAIImageGenerator()
        
        # Test with a simple prompt
        if tool.test_generation():
            print("✓ Image generation tool test passed")
            return True
        else:
            print("✗ Image generation tool test failed")
            return False
    except Exception as e:
        print(f"✗ Image generation tool test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_image_generation_tool()