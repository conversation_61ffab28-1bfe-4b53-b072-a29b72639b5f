"""
Base templates for Image Agent web interfaces.
Provides common HTML templates with responsive design.
"""

# Base HTML template with responsive design and common components
BASE_TEMPLATE = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ provider_name }} Image Agent{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-color);
            margin: 0;
            padding: 0;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .main-container {
            min-height: calc(100vh - 120px);
            padding: 2rem 0;
        }
        
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 12px;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 1.25rem;
        }
        
        .btn {
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .image-preview {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            background: white;
        }
        
        .generation-result {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        
        .progress-bar {
            border-radius: 4px;
            background: linear-gradient(90deg, var(--primary-color), #0056b3);
        }
        
        .alert {
            border: none;
            border-radius: 8px;
            padding: 1rem 1.5rem;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
        
        .file-drop-zone {
            border: 2px dashed #ced4da;
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: var(--primary-color);
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        .file-drop-zone.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        .history-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
            transition: box-shadow 0.3s ease;
        }
        
        .history-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-success { background-color: var(--success-color); }
        .status-processing { background-color: var(--warning-color); animation: pulse 2s infinite; }
        .status-error { background-color: var(--danger-color); }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .footer {
            background-color: var(--dark-color);
            color: white;
            text-align: center;
            padding: 1rem 0;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0;
            }
            
            .card {
                margin: 0 0.5rem;
            }
            
            .file-drop-zone {
                padding: 2rem 1rem;
            }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-paint-brush me-2"></i>
                {{ provider_name }} Image Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showHistory()">
                            <i class="fas fa-history me-1"></i>History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/health">
                            <i class="fas fa-heartbeat me-1"></i>Status
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-container">
        <div class="container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                &copy; {{ current_year }} {{ provider_name }} Image Agent | 
                Powered by AI | 
                <span id="session-info" class="text-muted">
                    {% if session_id %}Session: {{ session_id[:8] }}...{% else %}No Session{% endif %}
                </span>
            </p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Global variables
        const API_BASE = '/api';
        let currentSession = null;
        
        // Initialize session on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSession();
            setupFileDropZones();
        });
        
        // Session management
        async function initializeSession() {
            try {
                // Try to get existing session
                let response = await fetch(`${API_BASE}/session`);
                if (response.ok) {
                    currentSession = await response.json();
                } else {
                    // Create new session
                    response = await fetch(`${API_BASE}/session`, { method: 'POST' });
                    if (response.ok) {
                        currentSession = await response.json();
                    }
                }
                
                if (currentSession) {
                    updateSessionInfo(currentSession.session_id);
                }
            } catch (error) {
                console.error('Session initialization failed:', error);
                showAlert('warning', 'Could not initialize session. Some features may not work.');
            }
        }
        
        function updateSessionInfo(sessionId) {
            const sessionInfo = document.getElementById('session-info');
            if (sessionInfo) {
                sessionInfo.textContent = `Session: ${sessionId.substring(0, 8)}...`;
            }
        }
        
        // File upload and drag-drop
        function setupFileDropZones() {
            const dropZones = document.querySelectorAll('.file-drop-zone');
            dropZones.forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('dragleave', handleDragLeave);
                zone.addEventListener('drop', handleDrop);
            });
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }
        
        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0], e.currentTarget);
            }
        }
        
        async function handleFileSelect(file, dropZone = null) {
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showProgress('Uploading file...', 50);
                
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    handleFileUploadSuccess(result, dropZone);
                    hideProgress();
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                console.error('File upload error:', error);
                showAlert('danger', `File upload failed: ${error.message}`);
                hideProgress();
            }
        }
        
        function handleFileUploadSuccess(fileInfo, dropZone) {
            showAlert('success', `File uploaded successfully: ${fileInfo.filename}`);
            
            // Show preview if it's an image
            if (fileInfo.preview && dropZone) {
                const preview = document.createElement('img');
                preview.src = `data:image/jpeg;base64,${fileInfo.preview}`;
                preview.className = 'image-preview mt-3';
                preview.style.maxHeight = '200px';
                
                // Replace drop zone content with preview
                dropZone.innerHTML = '';
                dropZone.appendChild(preview);
                dropZone.classList.add('has-file');
            }
        }
        
        // Progress indication
        function showProgress(message, percentage = 0) {
            let progressContainer = document.getElementById('progress-container');
            if (!progressContainer) {
                progressContainer = document.createElement('div');
                progressContainer.id = 'progress-container';
                progressContainer.className = 'mb-3';
                document.querySelector('.container').insertBefore(progressContainer, document.querySelector('.container').firstChild);
            }
            
            progressContainer.innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="flex-grow-1">
                                <div class="mb-2">${message}</div>
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: ${percentage}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function updateProgress(message, percentage) {
            const progressContainer = document.getElementById('progress-container');
            if (progressContainer) {
                const messageEl = progressContainer.querySelector('.mb-2');
                const progressBar = progressContainer.querySelector('.progress-bar');
                
                if (messageEl) messageEl.textContent = message;
                if (progressBar) progressBar.style.width = `${percentage}%`;
            }
        }
        
        function hideProgress() {
            const progressContainer = document.getElementById('progress-container');
            if (progressContainer) {
                progressContainer.remove();
            }
        }
        
        // Alert system
        function showAlert(type, message, dismissible = true) {
            const alertContainer = document.createElement('div');
            alertContainer.className = `alert alert-${type}${dismissible ? ' alert-dismissible' : ''} fade show`;
            alertContainer.innerHTML = `
                ${message}
                ${dismissible ? '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' : ''}
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertContainer, container.firstChild);
            
            // Auto-hide after 5 seconds
            if (dismissible) {
                setTimeout(() => {
                    if (alertContainer.parentNode) {
                        alertContainer.remove();
                    }
                }, 5000);
            }
        }
        
        // History modal
        async function showHistory() {
            try {
                const response = await fetch(`${API_BASE}/history`);
                const history = await response.json();
                
                // Create modal content
                const modalContent = createHistoryModal(history);
                document.body.appendChild(modalContent);
                
                // Show modal
                const modal = new bootstrap.Modal(modalContent);
                modal.show();
                
                // Remove modal when hidden
                modalContent.addEventListener('hidden.bs.modal', () => {
                    modalContent.remove();
                });
                
            } catch (error) {
                console.error('History loading error:', error);
                showAlert('danger', 'Failed to load history');
            }
        }
        
        function createHistoryModal(history) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Generation History</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${history.length > 0 ? createHistoryItems(history) : '<p>No generation history found.</p>'}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;
            return modal;
        }
        
        function createHistoryItems(history) {
            return history.map(item => `
                <div class="history-item">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>${item.prompt || 'No prompt'}</h6>
                            <small class="text-muted">
                                ${new Date(item.created_at).toLocaleString()}
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="status-indicator status-${item.status}"></span>
                            ${item.status}
                        </div>
                    </div>
                    ${item.image_url ? `<img src="${item.image_url}" class="image-preview mt-2" style="max-height: 100px;">` : ''}
                </div>
            `).join('');
        }
        
        // Utility functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Clear session
        async function clearSession() {
            try {
                await fetch(`${API_BASE}/clear-session`, { method: 'POST' });
                showAlert('info', 'Session cleared successfully');
                setTimeout(() => location.reload(), 1000);
            } catch (error) {
                console.error('Clear session error:', error);
                showAlert('danger', 'Failed to clear session');
            }
        }
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>'''

INDEX_TEMPLATE = '''{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- Welcome Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-magic me-2"></i>
                    Welcome to {{ provider_name }} Image Agent
                </h4>
            </div>
            <div class="card-body">
                <p class="lead">
                    Create amazing images using advanced AI technology. 
                    Simply describe what you want to see, and watch your vision come to life!
                </p>
                
                <!-- Quick Start -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3">
                            <i class="fas fa-edit fa-2x text-primary mb-2"></i>
                            <h6>1. Describe</h6>
                            <p class="small">Enter your image description</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3">
                            <i class="fas fa-cog fa-2x text-primary mb-2"></i>
                            <h6>2. Configure</h6>
                            <p class="small">Adjust size and style</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3">
                            <i class="fas fa-download fa-2x text-primary mb-2"></i>
                            <h6>3. Generate</h6>
                            <p class="small">Create and download</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button class="btn btn-primary btn-lg" onclick="startGeneration()">
                        <i class="fas fa-play me-2"></i>Start Creating
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Generation Form -->
        <div class="card" id="generation-form" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-paint-brush me-2"></i>
                    Create Your Image
                </h5>
            </div>
            <div class="card-body">
                <form id="imageForm">
                    <!-- Prompt Input -->
                    <div class="mb-3">
                        <label for="prompt" class="form-label">
                            <i class="fas fa-comment me-1"></i>
                            Image Description *
                        </label>
                        <textarea 
                            class="form-control" 
                            id="prompt" 
                            name="prompt" 
                            rows="3" 
                            placeholder="Describe the image you want to generate..."
                            required
                        ></textarea>
                        <div class="form-text">
                            Be specific and detailed for best results
                        </div>
                    </div>
                    
                    <!-- Style and Options -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="style" class="form-label">
                                    <i class="fas fa-palette me-1"></i>
                                    Style
                                </label>
                                <select class="form-select" id="style" name="style">
                                    <option value="">Auto</option>
                                    <option value="realistic">Realistic</option>
                                    <option value="artistic">Artistic</option>
                                    <option value="cartoon">Cartoon</option>
                                    <option value="abstract">Abstract</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quality" class="form-label">
                                    <i class="fas fa-star me-1"></i>
                                    Quality
                                </label>
                                <select class="form-select" id="quality" name="quality">
                                    <option value="standard">Standard</option>
                                    <option value="high">High</option>
                                    <option value="ultra">Ultra</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Size Selection -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="width" class="form-label">
                                    <i class="fas fa-arrows-alt-h me-1"></i>
                                    Width
                                </label>
                                <input type="number" class="form-control" id="width" name="width" value="512" min="256" max="2048" step="64">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="height" class="form-label">
                                    <i class="fas fa-arrows-alt-v me-1"></i>
                                    Height
                                </label>
                                <input type="number" class="form-control" id="height" name="height" value="512" min="256" max="2048" step="64">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Quick Sizes</label>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setSize(512, 512)">1:1</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setSize(768, 512)">3:2</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="setSize(512, 768)">2:3</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Advanced Options (Collapsible) -->
                    <div class="mb-3">
                        <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                            <i class="fas fa-cog me-1"></i>Advanced Options
                        </button>
                        <div class="collapse" id="advancedOptions">
                            <div class="card card-body mt-2">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="steps" class="form-label">
                                                Inference Steps
                                            </label>
                                            <input type="range" class="form-range" id="steps" name="steps" min="10" max="100" value="50">
                                            <div class="form-text">Steps: <span id="stepsValue">50</span></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="guidance" class="form-label">
                                                Guidance Scale
                                            </label>
                                            <input type="range" class="form-range" id="guidance" name="guidance" min="1" max="20" value="7.5" step="0.5">
                                            <div class="form-text">Scale: <span id="guidanceValue">7.5</span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="seed" class="form-label">
                                        Seed (optional)
                                    </label>
                                    <input type="number" class="form-control" id="seed" name="seed" placeholder="Random seed for reproducibility">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reference Image Upload (Optional) -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-image me-1"></i>
                            Reference Image (Optional)
                        </label>
                        <div class="file-drop-zone" id="imageUpload">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <p>Drag and drop an image here, or click to select</p>
                            <input type="file" class="d-none" id="fileInput" accept="image/*">
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                            <i class="fas fa-magic me-2"></i>Generate Image
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>Reset
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Results Area -->
        <div id="resultsArea"></div>
    </div>
</div>

<script>
// Form handling
document.getElementById('imageForm').addEventListener('submit', handleImageGeneration);
document.getElementById('fileInput').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0], document.getElementById('imageUpload'));
    }
});

// Range input updates
document.getElementById('steps').addEventListener('input', function() {
    document.getElementById('stepsValue').textContent = this.value;
});
document.getElementById('guidance').addEventListener('input', function() {
    document.getElementById('guidanceValue').textContent = this.value;
});

// File drop zone click
document.getElementById('imageUpload').addEventListener('click', function() {
    document.getElementById('fileInput').click();
});

function startGeneration() {
    document.getElementById('generation-form').style.display = 'block';
    document.getElementById('generation-form').scrollIntoView({ behavior: 'smooth' });
}

function setSize(width, height) {
    document.getElementById('width').value = width;
    document.getElementById('height').value = height;
}

async function handleImageGeneration(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());
    
    // Validation
    try {
        const validationResponse = await fetch(`${API_BASE}/validate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        
        const validation = await validationResponse.json();
        if (!validation.valid) {
            showValidationErrors(validation.results);
            return;
        }
    } catch (error) {
        console.error('Validation error:', error);
    }
    
    // Start generation
    const generateBtn = document.getElementById('generateBtn');
    generateBtn.disabled = true;
    generateBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Generating...';
    
    try {
        showProgress('Starting image generation...', 10);
        
        const response = await fetch(`${API_BASE}/generate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            handleGenerationResult(result);
        } else {
            throw new Error(result.error || 'Generation failed');
        }
    } catch (error) {
        console.error('Generation error:', error);
        showAlert('danger', `Generation failed: ${error.message}`);
    } finally {
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Generate Image';
        hideProgress();
    }
}

function handleGenerationResult(result) {
    const resultsArea = document.getElementById('resultsArea');
    
    const resultCard = document.createElement('div');
    resultCard.className = 'card mt-4';
    resultCard.innerHTML = `
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-check-circle text-success me-2"></i>
                Generated Image
            </h5>
        </div>
        <div class="card-body text-center">
            <img src="${result.image_url}" class="img-fluid mb-3" style="max-height: 500px; border-radius: 8px;">
            <div class="mb-3">
                <strong>Prompt:</strong> ${result.prompt || 'N/A'}
            </div>
            <div class="btn-group">
                <a href="${result.download_url}" class="btn btn-success" download>
                    <i class="fas fa-download me-1"></i>Download
                </a>
                <button class="btn btn-outline-primary" onclick="shareImage('${result.image_url}')">
                    <i class="fas fa-share me-1"></i>Share
                </button>
                <button class="btn btn-outline-secondary" onclick="regenerateVariation('${result.prompt}')">
                    <i class="fas fa-redo me-1"></i>Regenerate
                </button>
            </div>
        </div>
    `;
    
    resultsArea.appendChild(resultCard);
    resultCard.scrollIntoView({ behavior: 'smooth' });
}

function showValidationErrors(results) {
    let errors = [];
    for (const [field, result] of Object.entries(results)) {
        if (!result.valid) {
            errors.push(`${field}: ${result.error}`);
        }
    }
    showAlert('warning', 'Please fix the following issues:<br>' + errors.join('<br>'));
}

function resetForm() {
    document.getElementById('imageForm').reset();
    document.getElementById('imageUpload').innerHTML = `
        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
        <p>Drag and drop an image here, or click to select</p>
    `;
    document.getElementById('imageUpload').classList.remove('has-file');
    document.getElementById('resultsArea').innerHTML = '';
}

function shareImage(imageUrl) {
    if (navigator.share) {
        navigator.share({
            title: 'AI Generated Image',
            url: imageUrl
        });
    } else {
        navigator.clipboard.writeText(imageUrl);
        showAlert('info', 'Image URL copied to clipboard');
    }
}

function regenerateVariation(prompt) {
    document.getElementById('prompt').value = prompt;
    startGeneration();
}
</script>
{% endblock %}'''

ERROR_TEMPLATE = '''{% extends "base.html" %}

{% block title %}Error {{ error_code }} - {{ provider_name }} Image Agent{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    {% if error_code == 404 %}
                        <i class="fas fa-search fa-4x text-warning"></i>
                    {% elif error_code == 413 %}
                        <i class="fas fa-file-upload fa-4x text-danger"></i>
                    {% else %}
                        <i class="fas fa-exclamation-triangle fa-4x text-danger"></i>
                    {% endif %}
                </div>
                
                <h1 class="display-4 mb-3">{{ error_code }}</h1>
                <p class="lead mb-4">{{ error_message }}</p>
                
                {% if error_code == 404 %}
                    <p>The page you're looking for doesn't exist.</p>
                {% elif error_code == 413 %}
                    <p>The file you tried to upload is too large. Please try a smaller file.</p>
                {% else %}
                    <p>Something went wrong. Please try again later.</p>
                {% endif %}
                
                <div class="mt-4">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''