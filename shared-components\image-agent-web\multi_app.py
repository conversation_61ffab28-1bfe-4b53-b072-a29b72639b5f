"""
Flask web application for the Langchain Image Generation Agent.
Provides a modern chat interface for interacting with the agent.
"""
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import base64
import json
from datetime import datetime
from dotenv import load_dotenv
from src.agent import ImageGenerationAgent
from src.prompt_enhancement_tool import PromptEnhancementTool
from langchain_core.messages import HumanMessage, AIMessage
from utils.image_encoder import ImageEncoder
from config.settings import Settings
import logging

# Load environment variables from .env file
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Global agent instance
agent = None
chat_history = []

# Global prompt enhancement tool instance
prompt_enhancer = None

# Global variable to store custom ruleset per session
custom_rulesets = {}

def initialize_agent():
    """Initialize the agent."""
    global agent
    try:
        agent = ImageGenerationAgent()
        logger.info("Agent initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize agent: {str(e)}")
        return False

def initialize_prompt_enhancer():
    """Initialize the prompt enhancement tool."""
    global prompt_enhancer
    try:
        if Settings.PROMPT_ENHANCEMENT_ENABLED:
            prompt_enhancer = PromptEnhancementTool()
            logger.info("Prompt enhancer initialized successfully")
            return True
        else:
            logger.info("Prompt enhancement is disabled in settings")
            return True
    except Exception as e:
        logger.error(f"Failed to initialize prompt enhancer: {str(e)}")
        return False

@app.route('/')
def index():
    """Serve the main chat interface."""
    return render_template('chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages."""
    global agent, chat_history, custom_rulesets

    if not agent:
        if not initialize_agent():
            return jsonify({
                'error': 'Agent initialization failed. Please check your API key and try again.'
            }), 500

    try:
        # Check for custom ruleset and apply it
        session_id = request.remote_addr
        if session_id in custom_rulesets:
            ruleset = custom_rulesets[session_id]
            # Apply ruleset to current request context
            if agent:
                agent.ensure_custom_ruleset(ruleset)
        data = request.get_json()
        user_message = data.get('message', '').strip()
        parameters = data.get('parameters', {})
        uploaded_images = data.get('images', [])
        
        if not user_message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Handle API mode selection
        api_mode = parameters.get('api_mode', 'responses')  # Default to responses API
        enhanced_message = user_message

        # Add API mode instruction to the message
        if api_mode == 'legacy':
            enhanced_message += " [Use OpenAIImageGenerator or OpenAIImageEditor tools only - no automatic prompt enhancement]"
        else:
            enhanced_message += " [Use ResponsesImageTool for advanced features]"

        # Add other parameters to the message if any are specified
        if parameters:
            # Remove api_mode from parameters since we handled it separately
            other_params = {k: v for k, v in parameters.items() if k != 'api_mode'}
            if other_params:
                param_string = ", ".join([f"{k}={v}" for k, v in other_params.items()])
                enhanced_message += f" [Additional parameters: {param_string}]"

        # Process uploaded images using ImageEncoder
        processed_images = None
        if uploaded_images:
            try:
                processed_images = ImageEncoder.process_uploaded_images(uploaded_images)
                success_count = processed_images['success_count']
                error_count = len(processed_images['errors'])

                if success_count > 0:
                    image_info = f" [Reference images: {success_count} image(s) processed and ready for use]"
                    enhanced_message += image_info
                    logger.info(f"Successfully processed {success_count}/{len(uploaded_images)} uploaded images")

                    if error_count > 0:
                        logger.warning(f"Failed to process {error_count} images: {processed_images['errors']}")
                else:
                    logger.error(f"Failed to process any uploaded images: {processed_images['errors']}")
                    return jsonify({'error': f'Failed to process uploaded images: {"; ".join(processed_images["errors"])}'}), 400

            except Exception as e:
                logger.error(f"Error processing uploaded images: {str(e)}")
                return jsonify({'error': f'Error processing uploaded images: {str(e)}'}), 400
        
        # Process with agent, passing processed images if available
        if processed_images:
            response = agent.invoke(enhanced_message, processed_images=processed_images)
        else:
            response = agent.invoke(enhanced_message)
        
        # Extract images if any were generated
        images = []
        if hasattr(response, 'get') and 'output' in response:
            # Look for base64 image data in response
            output_text = response['output']
            # Parse any base64 image data and save
            # This would need to be implemented based on your agent's response format
        
        # Prepare response data
        response_data = {
            'response': response['output'] if isinstance(response, dict) else str(response),
            'images': images,
            'uploaded_images_count': len(uploaded_images)
        }

        # Add processed image information
        if processed_images:
            response_data.update({
                'processed_images_count': processed_images['success_count'],
                'image_processing_errors': processed_images['errors']
            })

            # Clean up temporary files
            try:
                for temp_file in processed_images['temp_files']:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        logger.debug(f"Cleaned up temporary file: {temp_file}")
            except Exception as e:
                logger.warning(f"Error cleaning up temporary files: {str(e)}")

        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear', methods=['POST'])
def clear_history():
    """Clear chat history."""
    global chat_history
    chat_history = []
    return jsonify({'message': 'Chat history cleared'})

@app.route('/api/enhance-prompt', methods=['POST'])
def enhance_prompt():
    """Enhance a user prompt for better image generation."""
    global prompt_enhancer

    # Check if prompt enhancement is enabled
    if not Settings.PROMPT_ENHANCEMENT_ENABLED:
        return jsonify({
            'error': 'Prompt enhancement is disabled'
        }), 400

    # Initialize prompt enhancer if not already done
    if not prompt_enhancer:
        if not initialize_prompt_enhancer():
            return jsonify({
                'error': 'Prompt enhancement service is not available'
            }), 500

    try:
        data = request.get_json()
        original_prompt = data.get('prompt', '').strip()
        context = data.get('context', '')
        style_preference = data.get('style_preference', '')
        enhancement_level = data.get('enhancement_level', 'moderate')

        if not original_prompt:
            return jsonify({'error': 'Prompt is required'}), 400

        # Validate enhancement level
        valid_levels = ['light', 'moderate', 'extensive']
        if enhancement_level not in valid_levels:
            enhancement_level = 'moderate'

        logger.info(f"Enhancing prompt: '{original_prompt[:50]}...'")

        # Use the prompt enhancement tool
        result_json = prompt_enhancer._run(
            original_prompt=original_prompt,
            context=context if context else None,
            style_preference=style_preference if style_preference else None,
            enhancement_level=enhancement_level
        )

        # Parse the result
        import json
        result = json.loads(result_json)

        logger.info("Prompt enhancement completed successfully")
        return jsonify(result)

    except Exception as e:
        logger.error(f"Prompt enhancement error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'original_prompt': data.get('prompt', '') if 'data' in locals() else ''
        }), 500

@app.route('/api/set-custom-ruleset', methods=['POST'])
def set_custom_ruleset():
    """Set a custom ruleset for the agent."""
    global agent, custom_rulesets

    try:
        data = request.get_json()
        ruleset = data.get('ruleset', '').strip()

        if not ruleset:
            return jsonify({'error': 'Ruleset is required'}), 400

        # Validate ruleset length
        if len(ruleset) > 2000:
            return jsonify({'error': 'Ruleset exceeds maximum length of 2000 characters'}), 400

        # Basic prompt injection prevention
        forbidden_patterns = ['ignore previous', 'disregard above', 'forget all', 'system:', 'assistant:']
        ruleset_lower = ruleset.lower()
        for pattern in forbidden_patterns:
            if pattern in ruleset_lower:
                return jsonify({'error': f'Ruleset contains forbidden pattern: {pattern}'}), 400

        # Store ruleset for the session (using a simple session ID based on request)
        session_id = request.remote_addr  # Simple session tracking
        custom_rulesets[session_id] = ruleset

        # Re-initialize agent with custom ruleset if it exists
        if agent:
            agent.set_custom_ruleset(ruleset)
            logger.info(f"Custom ruleset applied for session {session_id}")

        return jsonify({
            'success': True,
            'message': 'Custom ruleset applied successfully',
            'ruleset_length': len(ruleset)
        })

    except Exception as e:
        logger.error(f"Error setting custom ruleset: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear-custom-ruleset', methods=['POST'])
def clear_custom_ruleset():
    """Clear the custom ruleset for the agent."""
    global agent, custom_rulesets

    try:
        session_id = request.remote_addr

        # Remove custom ruleset
        if session_id in custom_rulesets:
            del custom_rulesets[session_id]

        # Reset agent to default behavior
        if agent:
            agent.clear_custom_ruleset()
            logger.info(f"Custom ruleset cleared for session {session_id}")

        return jsonify({
            'success': True,
            'message': 'Custom ruleset cleared successfully'
        })

    except Exception as e:
        logger.error(f"Error clearing custom ruleset: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/images/<filename>')
def serve_image(filename):
    """Serve generated images."""
    return send_from_directory('generated_images', filename)

@app.route('/api/images')
def list_images():
    """List all available images."""
    try:
        images = []
        if os.path.exists('generated_images'):
            for filename in os.listdir('generated_images'):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
                    file_path = os.path.join('generated_images', filename)
                    file_stats = os.stat(file_path)
                    images.append({
                        'filename': filename,
                        'url': f'/images/{filename}',
                        'size': file_stats.st_size,
                        'created': datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    })
        
        # Sort by creation time, newest first
        images.sort(key=lambda x: x['created'], reverse=True)
        
        return jsonify({'images': images})
    except Exception as e:
        logger.error(f"Error listing images: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health')
def health():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'agent_initialized': agent is not None,
        'timestamp': datetime.now().isoformat()
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('generated_images', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Initialize agent on startup
    print("🚀 Starting Langchain Image Generation Agent Web Interface...")
    print("⚙️  Initializing agent...")

    agent_ok = initialize_agent()
    enhancer_ok = initialize_prompt_enhancer()

    if agent_ok:
        print("✅ Agent initialized successfully!")
        if enhancer_ok and Settings.PROMPT_ENHANCEMENT_ENABLED:
            print("✅ Prompt enhancer initialized successfully!")
        elif Settings.PROMPT_ENHANCEMENT_ENABLED:
            print("⚠️  Prompt enhancer failed to initialize")
        else:
            print("ℹ️  Prompt enhancement disabled")
        print("🌐 Starting web server...")
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        print("❌ Failed to initialize agent. Please check your configuration.")
