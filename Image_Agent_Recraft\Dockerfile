FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for image processing and vector graphics
RUN apt-get update && apt-get install -y \
    libcairo2-dev \
    libgdk-pixbuf2.0-dev \
    libffi-dev \
    shared-mime-info \
    libmagickwand-dev \
    libxml2-dev \
    libxslt-dev \
    librsvg2-bin \
    potrace \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p generated_images uploads logs

# Set environment variables
ENV PYTHONPATH=/app
ENV FLASK_APP=web_app.py
ENV FLASK_ENV=production

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/health', timeout=10)"

# Expose port
EXPOSE 5000

# Start command
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--timeout", "300", "--keepalive", "2", "web_app:app"]