{% extends "base.html" %}

{% block title %}Vector Graphics - Recraft Vector Graphics Agent{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-bezier-curve me-2"></i>Vector Graphics Generation
                </h2>
                <p class="mb-0 text-muted">Create scalable SVG and PDF graphics for professional use</p>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <form id="vector-form">
                            <div class="mb-3">
                                <label for="vector-prompt" class="form-label">Describe your vector graphic:</label>
                                <textarea 
                                    class="form-control" 
                                    id="vector-prompt" 
                                    rows="4" 
                                    placeholder="e.g., Create a modern logo for a coffee shop with a minimalist coffee cup icon, use earth tones"
                                    required
                                ></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="vector-style" class="form-label">Vector Style:</label>
                                    <select class="form-select" id="vector-style">
                                        <option value="vector_illustration">Vector Illustration</option>
                                        <option value="icon">Icon</option>
                                        <option value="digital_illustration">Digital Illustration</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="vector-format" class="form-label">Output Format:</label>
                                    <select class="form-select" id="vector-format">
                                        <option value="svg">SVG (Scalable Vector)</option>
                                        <option value="pdf">PDF (Print Ready)</option>
                                        <option value="both">Both SVG & PDF</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="vector-size" class="form-label">Canvas Size:</label>
                                    <select class="form-select" id="vector-size">
                                        <option value="1024x1024">Square (1024x1024)</option>
                                        <option value="1365x1024">Landscape (1365x1024)</option>
                                        <option value="1024x1365">Portrait (1024x1365)</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="color-scheme" class="form-label">Color Scheme:</label>
                                    <select class="form-select" id="color-scheme">
                                        <option value="">Auto-select</option>
                                        <option value="monochrome">Monochrome</option>
                                        <option value="minimal">Minimal Colors</option>
                                        <option value="vibrant">Vibrant</option>
                                        <option value="professional">Professional</option>
                                    </select>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-recraft w-100" id="vector-generate-btn">
                                <i class="fas fa-bezier-curve me-2"></i>Generate Vector Graphics
                            </button>
                        </form>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card bg-light h-100">
                            <div class="card-body">
                                <h5><i class="fas fa-info-circle me-2"></i>Vector Graphics Features</h5>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>Scalable:</strong> Perfect quality at any size
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>Print Ready:</strong> High-resolution PDF output
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>Web Optimized:</strong> Clean SVG code
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>Editable:</strong> Modify colors and elements
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>Professional:</strong> Brand-quality designs
                                    </li>
                                </ul>
                                
                                <h6 class="mt-4">Best For:</h6>
                                <div class="d-flex flex-wrap gap-2">
                                    <span class="badge bg-primary">Logos</span>
                                    <span class="badge bg-primary">Icons</span>
                                    <span class="badge bg-primary">Business Cards</span>
                                    <span class="badge bg-primary">Letterheads</span>
                                    <span class="badge bg-primary">Banners</span>
                                    <span class="badge bg-primary">Web Graphics</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Animation -->
<div class="loading-spinner" id="vector-loading">
    <div class="text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Generating vector graphics...</span>
        </div>
        <h5>Creating Your Vector Graphics</h5>
        <p class="text-muted">This may take a moment for high-quality results...</p>
    </div>
</div>

<!-- Results Section -->
<div id="vector-results" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="mb-0">
                <i class="fas fa-star me-2"></i>Generated Vector Graphics
            </h3>
            <button class="btn btn-outline-secondary btn-sm" onclick="downloadAll()">
                <i class="fas fa-download me-1"></i>Download All
            </button>
        </div>
        <div class="card-body" id="vector-results-content">
            <!-- Results will be displayed here -->
        </div>
    </div>
</div>

<!-- Examples Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Vector Graphics Examples
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-store text-primary fa-3x mb-3"></i>
                                <h5>Business Logo</h5>
                                <p class="text-muted small">Professional logo for a local business with clean typography and brand colors</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="useExample('business-logo')">
                                    Use This Example
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-mobile-alt text-success fa-3x mb-3"></i>
                                <h5>App Icon Set</h5>
                                <p class="text-muted small">Modern icon set for mobile app interface, consistent style and sizing</p>
                                <button class="btn btn-outline-success btn-sm" onclick="useExample('app-icons')">
                                    Use This Example
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-certificate text-warning fa-3x mb-3"></i>
                                <h5>Brand Badge</h5>
                                <p class="text-muted small">Decorative badge or seal design for certificates, awards, or quality marks</p>
                                <button class="btn btn-outline-warning btn-sm" onclick="useExample('brand-badge')">
                                    Use This Example
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Vector form handling
document.getElementById('vector-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const prompt = document.getElementById('vector-prompt').value.trim();
    if (!prompt) {
        showToast('Please describe your vector graphic', 'warning');
        return;
    }
    
    const style = document.getElementById('vector-style').value;
    const format = document.getElementById('vector-format').value;
    const size = document.getElementById('vector-size').value;
    const colorScheme = document.getElementById('color-scheme').value;
    
    // Build enhanced prompt
    let enhancedPrompt = `Create vector graphics: ${prompt}`;
    if (colorScheme) {
        enhancedPrompt += ` Use ${colorScheme} color scheme.`;
    }
    enhancedPrompt += ` Generate vector output in ${format} format.`;
    
    // Show loading
    document.getElementById('vector-generate-btn').disabled = true;
    showLoading('vector-loading');
    hideVectorResults();
    
    try {
        const options = {
            vector_output: true,
            style: style,
            size: size,
            format: format,
            color_scheme: colorScheme
        };
        
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: enhancedPrompt,
                options: options,
                session_id: sessionId
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showVectorResults(data.response);
            showToast('Vector graphics generated successfully!', 'success');
        } else {
            throw new Error(data.error || 'Vector generation failed');
        }
        
    } catch (error) {
        handleApiError(error, 'Vector generation');
    } finally {
        document.getElementById('vector-generate-btn').disabled = false;
        hideLoading('vector-loading');
    }
});

function showVectorResults(content) {
    document.getElementById('vector-results-content').innerHTML = content;
    document.getElementById('vector-results').style.display = 'block';
    document.getElementById('vector-results').scrollIntoView({ behavior: 'smooth' });
}

function hideVectorResults() {
    document.getElementById('vector-results').style.display = 'none';
}

function useExample(type) {
    const examples = {
        'business-logo': 'Create a professional logo for a local coffee shop called "Brew & Bean". Use a minimalist coffee cup icon with steam, warm earth tones (brown, cream, green), clean sans-serif typography. The design should work well on business cards and storefront signage.',
        'app-icons': 'Design a set of 6 modern app icons for a productivity mobile app: calendar, tasks, notes, timer, settings, and profile. Use consistent rounded square backgrounds, clean line art style, and a blue color scheme (#007AFF). Icons should be simple and recognizable at small sizes.',
        'brand-badge': 'Create an elegant circular badge design for a premium organic food brand. Include decorative border elements, ribbon banner for text, and natural motifs like leaves or wheat. Use green and gold colors with vintage-inspired typography. Suitable for product labels and certificates.'
    };
    
    const prompt = examples[type];
    if (prompt) {
        document.getElementById('vector-prompt').value = prompt;
        document.getElementById('vector-prompt').focus();
        showToast('Example loaded! You can modify it as needed.', 'info');
    }
}

function downloadAll() {
    // This would implement downloading all generated vector files
    showToast('Download feature will be available soon!', 'info');
}
</script>
{% endblock %}