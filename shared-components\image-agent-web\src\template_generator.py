"""
Template utilities for creating provider-specific templates.
Helps generate customized HTML templates from base templates.
"""
import os
import re
from pathlib import Path
from typing import Dict, Any, List, Optional
from .base_templates import BASE_TEMPLATE, INDEX_TEMPLATE, ERROR_TEMPLATE


class TemplateGenerator:
    """Generate provider-specific templates from base templates."""
    
    def __init__(self, provider_name: str, provider_color: str = "#007bff"):
        """
        Initialize template generator.
        
        Args:
            provider_name: Name of the AI provider
            provider_color: Primary color for the provider theme
        """
        self.provider_name = provider_name
        self.provider_color = provider_color
        self.templates = {
            'base.html': BASE_TEMPLATE,
            'index.html': INDEX_TEMPLATE,
            'error.html': ERROR_TEMPLATE
        }
    
    def generate_all_templates(self, output_dir: str) -> Dict[str, str]:
        """
        Generate all templates with provider customization.
        
        Args:
            output_dir: Directory to save templates
            
        Returns:
            Dictionary mapping template names to file paths
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        generated_files = {}
        
        for template_name, template_content in self.templates.items():
            # Customize template
            customized_content = self._customize_template(template_content)
            
            # Save to file
            file_path = output_path / template_name
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(customized_content)
            
            generated_files[template_name] = str(file_path)
        
        return generated_files
    
    def _customize_template(self, template_content: str) -> str:
        """
        Customize template with provider-specific elements.
        
        Args:
            template_content: Original template content
            
        Returns:
            Customized template content
        """
        # Replace provider name
        content = template_content.replace('{{ provider_name }}', self.provider_name)
        
        # Replace color scheme
        content = self._update_color_scheme(content)
        
        # Add provider-specific features
        content = self._add_provider_features(content)
        
        return content
    
    def _update_color_scheme(self, content: str) -> str:
        """Update CSS color scheme for provider branding."""
        # Replace primary color
        content = content.replace('--primary-color: #007bff;', f'--primary-color: {self.provider_color};')
        
        # Update gradients
        color_variations = self._generate_color_variations(self.provider_color)
        content = content.replace('#0056b3', color_variations['dark'])
        content = content.replace('rgba(0, 123, 255, 0.3)', color_variations['light_alpha'])
        content = content.replace('rgba(0, 123, 255, 0.25)', color_variations['lighter_alpha'])
        content = content.replace('rgba(0, 123, 255, 0.05)', color_variations['lightest_alpha'])
        content = content.replace('rgba(0, 123, 255, 0.1)', color_variations['very_light_alpha'])
        
        return content
    
    def _generate_color_variations(self, hex_color: str) -> Dict[str, str]:
        """Generate color variations for theming."""
        # Remove # if present
        hex_color = hex_color.lstrip('#')
        
        # Convert to RGB
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        
        # Generate variations
        dark_factor = 0.8
        dark_r = int(r * dark_factor)
        dark_g = int(g * dark_factor)
        dark_b = int(b * dark_factor)
        
        return {
            'original': f'#{hex_color}',
            'dark': f'#{dark_r:02x}{dark_g:02x}{dark_b:02x}',
            'light_alpha': f'rgba({r}, {g}, {b}, 0.3)',
            'lighter_alpha': f'rgba({r}, {g}, {b}, 0.25)',
            'lightest_alpha': f'rgba({r}, {g}, {b}, 0.05)',
            'very_light_alpha': f'rgba({r}, {g}, {b}, 0.1)',
        }
    
    def _add_provider_features(self, content: str) -> str:
        """Add provider-specific features to templates."""
        # This can be overridden by specific providers
        return content
    
    def add_custom_template(self, name: str, content: str) -> None:
        """
        Add a custom template.
        
        Args:
            name: Template name (should end with .html)
            content: Template content
        """
        self.templates[name] = content
    
    def create_provider_specific_forms(self) -> Dict[str, str]:
        """Create provider-specific form templates."""
        forms = {}
        
        # Basic generation form
        forms['generation_form.html'] = self._create_generation_form()
        
        # Settings form
        forms['settings_form.html'] = self._create_settings_form()
        
        # History view
        forms['history_view.html'] = self._create_history_view()
        
        return forms
    
    def _create_generation_form(self) -> str:
        """Create basic generation form template."""
        return '''
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Generate Image</h5>
            </div>
            <div class="card-body">
                <form id="generationForm" method="post">
                    <div class="mb-3">
                        <label for="prompt" class="form-label">Prompt</label>
                        <textarea class="form-control" id="prompt" name="prompt" rows="3" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="width" class="form-label">Width</label>
                                <input type="number" class="form-control" id="width" name="width" value="512">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="height" class="form-label">Height</label>
                                <input type="number" class="form-control" id="height" name="height" value="512">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Generate</button>
                </form>
            </div>
        </div>
        '''
    
    def _create_settings_form(self) -> str:
        """Create settings form template."""
        return '''
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Settings</h5>
            </div>
            <div class="card-body">
                <form id="settingsForm">
                    <div class="mb-3">
                        <label for="apiKey" class="form-label">API Key</label>
                        <input type="password" class="form-control" id="apiKey" name="apiKey">
                    </div>
                    
                    <div class="mb-3">
                        <label for="defaultStyle" class="form-label">Default Style</label>
                        <select class="form-select" id="defaultStyle" name="defaultStyle">
                            <option value="">Auto</option>
                            <option value="realistic">Realistic</option>
                            <option value="artistic">Artistic</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </form>
            </div>
        </div>
        '''
    
    def _create_history_view(self) -> str:
        """Create history view template."""
        return '''
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Generation History</h5>
            </div>
            <div class="card-body">
                <div id="historyContainer">
                    <!-- History items will be loaded here -->
                </div>
            </div>
        </div>
        '''


class OpenAITemplateGenerator(TemplateGenerator):
    """OpenAI-specific template generator."""
    
    def __init__(self):
        super().__init__("OpenAI DALL-E", "#00A67E")
    
    def _add_provider_features(self, content: str) -> str:
        """Add OpenAI-specific features."""
        # Add DALL-E specific options
        if 'Advanced Options' in content:
            dalle_options = '''
                <div class="mb-3">
                    <label for="model" class="form-label">Model</label>
                    <select class="form-select" id="model" name="model">
                        <option value="dall-e-3">DALL-E 3</option>
                        <option value="dall-e-2">DALL-E 2</option>
                    </select>
                </div>
            '''
            content = content.replace('</div>\n                            </div>\n                        </div>', 
                                    dalle_options + '</div>\n                            </div>\n                        </div>')
        
        return content


class RecraftTemplateGenerator(TemplateGenerator):
    """Recraft-specific template generator."""
    
    def __init__(self):
        super().__init__("Recraft", "#FF6B35")
    
    def _add_provider_features(self, content: str) -> str:
        """Add Recraft-specific features."""
        # Add vector graphics options
        if 'Style' in content and 'select' in content:
            recraft_styles = '''
                    <option value="vector_illustration">Vector Illustration</option>
                    <option value="digital_artwork">Digital Artwork</option>
                    <option value="realistic_image">Realistic Image</option>
            '''
            content = content.replace('<option value="abstract">Abstract</option>', 
                                    '<option value="abstract">Abstract</option>\n' + recraft_styles)
        
        return content


class ImagenTemplateGenerator(TemplateGenerator):
    """Google Imagen-specific template generator."""
    
    def __init__(self):
        super().__init__("Google Imagen", "#4285F4")
    
    def _add_provider_features(self, content: str) -> str:
        """Add Imagen-specific features."""
        # Add safety settings
        if 'Advanced Options' in content:
            imagen_options = '''
                <div class="mb-3">
                    <label for="safetyLevel" class="form-label">Safety Level</label>
                    <select class="form-select" id="safetyLevel" name="safetyLevel">
                        <option value="default">Default</option>
                        <option value="strict">Strict</option>
                        <option value="permissive">Permissive</option>
                    </select>
                </div>
            '''
            content = content.replace('</div>\n                            </div>\n                        </div>', 
                                    imagen_options + '</div>\n                            </div>\n                        </div>')
        
        return content


class FluxTemplateGenerator(TemplateGenerator):
    """FLUX.1-specific template generator."""
    
    def __init__(self):
        super().__init__("FLUX.1", "#8B5CF6")
    
    def _add_provider_features(self, content: str) -> str:
        """Add FLUX-specific features."""
        # Add model variants
        if 'Advanced Options' in content:
            flux_options = '''
                <div class="mb-3">
                    <label for="variant" class="form-label">Model Variant</label>
                    <select class="form-select" id="variant" name="variant">
                        <option value="pro">FLUX.1-pro</option>
                        <option value="dev">FLUX.1-dev</option>
                        <option value="schnell">FLUX.1-schnell</option>
                    </select>
                </div>
            '''
            content = content.replace('</div>\n                            </div>\n                        </div>', 
                                    flux_options + '</div>\n                            </div>\n                        </div>')
        
        return content


def create_templates_for_provider(provider: str, output_dir: str) -> Dict[str, str]:
    """
    Create templates for a specific provider.
    
    Args:
        provider: Provider name (openai, recraft, imagen, flux)
        output_dir: Directory to save templates
        
    Returns:
        Dictionary mapping template names to file paths
    """
    generators = {
        'openai': OpenAITemplateGenerator,
        'recraft': RecraftTemplateGenerator,
        'imagen': ImagenTemplateGenerator,
        'flux': FluxTemplateGenerator,
    }
    
    if provider.lower() not in generators:
        raise ValueError(f"Unknown provider: {provider}")
    
    generator = generators[provider.lower()]()
    return generator.generate_all_templates(output_dir)


def create_static_assets(output_dir: str) -> Dict[str, str]:
    """
    Create common static assets (CSS, JS, images).
    
    Args:
        output_dir: Directory to save static assets
        
    Returns:
        Dictionary mapping asset names to file paths
    """
    static_path = Path(output_dir)
    static_path.mkdir(parents=True, exist_ok=True)
    
    assets = {}
    
    # Create CSS directory and files
    css_dir = static_path / 'css'
    css_dir.mkdir(exist_ok=True)
    
    # Custom CSS
    custom_css = '''
/* Additional custom styles for Image Agents */

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.image-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.image-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.image-card-body {
    padding: 1rem;
}

.generation-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

.prompt-input {
    min-height: 100px;
    resize: vertical;
}

.parameter-slider {
    margin-bottom: 1rem;
}

.parameter-value {
    font-weight: 500;
    color: var(--primary-color);
}

.upload-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    margin-top: 1rem;
}

@media (max-width: 576px) {
    .image-grid {
        grid-template-columns: 1fr;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 0.5rem;
    }
}

/* Animation for loading states */
@keyframes shimmer {
    0% { background-position: -468px 0; }
    100% { background-position: 468px 0; }
}

.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
    background-size: 400% 100%;
    animation: shimmer 1.5s ease-in-out infinite;
    height: 200px;
    border-radius: 8px;
}
'''
    
    css_file = css_dir / 'custom.css'
    with open(css_file, 'w') as f:
        f.write(custom_css)
    assets['css/custom.css'] = str(css_file)
    
    # Create JS directory and files
    js_dir = static_path / 'js'
    js_dir.mkdir(exist_ok=True)
    
    # Custom JavaScript
    custom_js = '''
// Additional JavaScript utilities for Image Agents

class ImageAgent {
    constructor(apiBase = '/api') {
        this.apiBase = apiBase;
        this.currentGenerations = new Map();
    }
    
    async generateImage(params) {
        try {
            const response = await fetch(`${this.apiBase}/generate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(params)
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Generation failed');
            }
            
            return result;
        } catch (error) {
            console.error('Generation error:', error);
            throw error;
        }
    }
    
    async checkStatus(taskId) {
        try {
            const response = await fetch(`${this.apiBase}/status/${taskId}`);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Status check failed');
            }
            
            return result;
        } catch (error) {
            console.error('Status check error:', error);
            throw error;
        }
    }
    
    async pollStatus(taskId, onUpdate, maxAttempts = 60) {
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            try {
                const status = await this.checkStatus(taskId);
                
                if (onUpdate) {
                    onUpdate(status);
                }
                
                if (status.status === 'completed' || status.status === 'failed') {
                    return status;
                }
                
                // Wait 2 seconds before next poll
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.error(`Status poll attempt ${attempt + 1} failed:`, error);
                
                if (attempt === maxAttempts - 1) {
                    throw error;
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        throw new Error('Polling timeout');
    }
}

// Utility functions
function createImageCard(imageData) {
    const card = document.createElement('div');
    card.className = 'image-card';
    
    card.innerHTML = `
        <img src="${imageData.url}" alt="${imageData.prompt || 'Generated image'}">
        <div class="image-card-body">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <span class="generation-status status-${imageData.status}">${imageData.status}</span>
                <small class="text-muted">${new Date(imageData.created_at).toLocaleString()}</small>
            </div>
            <p class="card-text small">${imageData.prompt || 'No prompt provided'}</p>
            <div class="btn-group w-100">
                <a href="${imageData.download_url}" class="btn btn-sm btn-primary" download>Download</a>
                <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('${imageData.url}')">Copy URL</button>
            </div>
        </div>
    `;
    
    return card;
}

function createLoadingCard() {
    const card = document.createElement('div');
    card.className = 'image-card';
    
    card.innerHTML = `
        <div class="loading-placeholder"></div>
        <div class="image-card-body">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2"></div>
                <span>Generating...</span>
            </div>
        </div>
    `;
    
    return card;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('URL copied to clipboard', 'success');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showToast('Failed to copy URL', 'error');
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

function validateImageDimensions(width, height, maxWidth = 2048, maxHeight = 2048) {
    const errors = [];
    
    if (width < 64 || width > maxWidth) {
        errors.push(`Width must be between 64 and ${maxWidth}`);
    }
    
    if (height < 64 || height > maxHeight) {
        errors.push(`Height must be between 64 and ${maxHeight}`);
    }
    
    if (width % 8 !== 0) {
        errors.push('Width must be divisible by 8');
    }
    
    if (height % 8 !== 0) {
        errors.push('Height must be divisible by 8');
    }
    
    return errors;
}

function formatPrompt(prompt, maxLength = 1000) {
    if (!prompt) return '';
    
    // Trim whitespace
    prompt = prompt.trim();
    
    // Truncate if too long
    if (prompt.length > maxLength) {
        prompt = prompt.substring(0, maxLength - 3) + '...';
    }
    
    return prompt;
}

// Initialize global image agent
const imageAgent = new ImageAgent();
'''
    
    js_file = js_dir / 'custom.js'
    with open(js_file, 'w') as f:
        f.write(custom_js)
    assets['js/custom.js'] = str(js_file)
    
    return assets