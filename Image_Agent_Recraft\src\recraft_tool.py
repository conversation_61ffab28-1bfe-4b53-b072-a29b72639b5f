"""
Recraft API integration tool for image generation and processing.
Based on the official Recraft API documentation and ComfyUI integration patterns.
"""
import io
import os
import requests
import logging
from typing import Optional, Dict, Any, Union, List
from datetime import datetime
from PIL import Image
import base64
from config.settings import Settings

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RecraftAPIError(Exception):
    """Custom exception for Recraft API errors."""
    pass


class RecraftClient:
    """
    Client for interacting with the Recraft API.
    Supports image generation, image-to-image, background processing, upscaling, and vector operations.
    """
    
    def __init__(self, api_key: Optional[str] = None, api_url: Optional[str] = None):
        """
        Initialize the Recraft API client.
        
        Args:
            api_key: Recraft API key (defaults to Settings.RECRAFT_API_KEY)
            api_url: Recraft API base URL (defaults to Settings.RECRAFT_API_URL)
        """
        self.api_key = api_key or Settings.RECRAFT_API_KEY
        self.api_url = (api_url or Settings.RECRAFT_API_URL).rstrip('/')
        
        if not self.api_key:
            raise RecraftAPIError("Recraft API key is required")
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'X-Client-Type': Settings.RECRAFT_CLIENT_TYPE,
        }
        
        # Validate connection on initialization
        self._validate_connection()
    
    def _validate_connection(self) -> bool:
        """Validate the API connection."""
        try:
            response = requests.get(
                f"{self.api_url}/status",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                logger.info("Recraft API connection validated successfully")
                return True
            else:
                logger.warning(f"API connection validation returned status {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Failed to validate Recraft API connection: {str(e)}")
            raise RecraftAPIError(f"API connection validation failed: {str(e)}")
    
    def _make_image_data(self, image_input: Union[str, Image.Image, bytes], is_mask: bool = False) -> bytes:
        """
        Convert various image inputs to bytes for API submission.
        
        Args:
            image_input: Image path, PIL Image, or raw bytes
            is_mask: Whether this is a mask image (applies threshold)
            
        Returns:
            bytes: Image data as bytes
        """
        if isinstance(image_input, str):
            # File path
            if not os.path.exists(image_input):
                raise RecraftAPIError(f"Image file not found: {image_input}")
            
            with open(image_input, 'rb') as f:
                image_data = f.read()
        
        elif isinstance(image_input, Image.Image):
            # PIL Image
            buffer = io.BytesIO()
            # Convert to RGB if necessary
            if image_input.mode in ('RGBA', 'LA', 'P'):
                image_input = image_input.convert('RGB')
            image_input.save(buffer, format='PNG')
            image_data = buffer.getvalue()
        
        elif isinstance(image_input, bytes):
            # Raw bytes
            image_data = image_input
        
        else:
            raise RecraftAPIError(f"Unsupported image input type: {type(image_input)}")
        
        # Apply mask processing if needed
        if is_mask and isinstance(image_input, (str, Image.Image)):
            try:
                # Load image for mask processing
                if isinstance(image_input, str):
                    img = Image.open(image_input)
                else:
                    img = image_input
                
                # Convert to grayscale and apply threshold
                mask_img = img.convert('L')
                mask_array = np.array(mask_img)
                mask_array = np.where(mask_array >= 127.5, 255, 0).astype(np.uint8)
                
                mask_img = Image.fromarray(mask_array)
                buffer = io.BytesIO()
                mask_img.save(buffer, format='PNG')
                image_data = buffer.getvalue()
                
            except Exception as e:
                logger.warning(f"Mask processing failed, using original: {e}")
        
        return image_data
    
    def _handle_api_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        Handle API response and extract image URLs or data.
        
        Args:
            response: requests Response object
            
        Returns:
            dict: Parsed response data
        """
        try:
            data = response.json()
        except Exception as e:
            raise RecraftAPIError(f"Failed to parse API response: {str(e)}")
        
        # Check for API errors
        if 'code' in data:
            error_msg = data.get('message', 'Unknown API error')
            raise RecraftAPIError(f"Recraft API Error: {error_msg}")
        
        # Extract image URL from different response formats
        if 'image' in data:
            return {'url': data['image']['url'], 'raw_response': data}
        elif 'data' in data and len(data['data']) > 0:
            return {'url': data['data'][0]['url'], 'raw_response': data}
        else:
            raise RecraftAPIError("No image URL found in API response")
    
    def generate_image(
        self, 
        prompt: str,
        model: Optional[str] = None,
        size: Optional[str] = None,
        style: Optional[str] = None,
        substyle: Optional[str] = None,
        random_seed: Optional[int] = None
    ) -> str:
        """
        Generate an image from a text prompt.
        
        Args:
            prompt: Text description of the image to generate
            model: Model to use (defaults to Settings.RECRAFT_MODEL)
            size: Image size (e.g., '1024x1024')
            style: Image style
            substyle: Image substyle
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the generated image
        """
        if not prompt or not prompt.strip():
            raise RecraftAPIError("Prompt is required for image generation")
        
        payload = {
            'prompt': prompt.strip(),
            'model': model or Settings.RECRAFT_MODEL or None,
            'style': style or Settings.DEFAULT_STYLE or None,
            'substyle': substyle or Settings.DEFAULT_SUBSTYLE or None,
            'size': size or Settings.DEFAULT_IMAGE_SIZE or None,
            'random_seed': random_seed or None,
        }
        
        # Remove None values
        payload = {k: v for k, v in payload.items() if v is not None}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/generations",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Image generated successfully: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"API request failed: {str(e)}")
    
    def image_to_image(
        self,
        image_input: Union[str, Image.Image, bytes],
        prompt: str,
        strength: float = 0.5,
        style: Optional[str] = None,
        substyle: Optional[str] = None,
        random_seed: Optional[int] = None
    ) -> str:
        """
        Transform an input image using a text prompt.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            prompt: Text description of desired changes
            strength: Transformation strength (0.0 to 1.0)
            style: Image style
            substyle: Image substyle
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the transformed image
        """
        if not prompt or not prompt.strip():
            raise RecraftAPIError("Prompt is required for image-to-image transformation")
        
        image_data = self._make_image_data(image_input)
        
        data = {
            'prompt': prompt.strip(),
            'strength': max(0.0, min(1.0, strength)),
            'style': style or Settings.DEFAULT_STYLE or None,
            'substyle': substyle or Settings.DEFAULT_SUBSTYLE or None,
            'random_seed': random_seed or None,
        }
        
        # Remove None values
        data = {k: v for k, v in data.items() if v is not None}
        
        files = {'image': io.BytesIO(image_data)}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/imageToImage",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Image-to-image transformation completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Image-to-image API request failed: {str(e)}")
    
    def remove_background(
        self,
        image_input: Union[str, Image.Image, bytes],
        random_seed: Optional[int] = None
    ) -> str:
        """
        Remove background from an image.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the image with background removed
        """
        image_data = self._make_image_data(image_input)
        
        data = {}
        if random_seed is not None:
            data['random_seed'] = random_seed
        
        files = {'image': io.BytesIO(image_data)}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/removeBackground",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Background removal completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Background removal API request failed: {str(e)}")
    
    def replace_background(
        self,
        image_input: Union[str, Image.Image, bytes],
        prompt: str,
        style: Optional[str] = None,
        substyle: Optional[str] = None,
        random_seed: Optional[int] = None
    ) -> str:
        """
        Replace image background based on a text prompt.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            prompt: Text description of the desired background
            style: Image style
            substyle: Image substyle
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the image with replaced background
        """
        if not prompt or not prompt.strip():
            raise RecraftAPIError("Prompt is required for background replacement")
        
        image_data = self._make_image_data(image_input)
        
        data = {
            'prompt': prompt.strip(),
            'style': style or Settings.DEFAULT_STYLE or None,
            'substyle': substyle or Settings.DEFAULT_SUBSTYLE or None,
            'random_seed': random_seed or None,
        }
        
        # Remove None values
        data = {k: v for k, v in data.items() if v is not None}
        
        files = {'image': io.BytesIO(image_data)}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/replaceBackground",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Background replacement completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Background replacement API request failed: {str(e)}")
    
    def crisp_upscale(
        self,
        image_input: Union[str, Image.Image, bytes],
        random_seed: Optional[int] = None
    ) -> str:
        """
        Upscale an image with crisp quality enhancement.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the upscaled image
        """
        image_data = self._make_image_data(image_input)
        
        data = {}
        if random_seed is not None:
            data['random_seed'] = random_seed
        
        files = {'image': io.BytesIO(image_data)}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/crispUpscale",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Crisp upscaling completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Crisp upscale API request failed: {str(e)}")
    
    def creative_upscale(
        self,
        image_input: Union[str, Image.Image, bytes],
        random_seed: Optional[int] = None
    ) -> str:
        """
        Upscale an image with creative enhancement.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the creatively upscaled image
        """
        image_data = self._make_image_data(image_input)
        
        data = {}
        if random_seed is not None:
            data['random_seed'] = random_seed
        
        files = {'image': io.BytesIO(image_data)}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/creativeUpscale",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Creative upscaling completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Creative upscale API request failed: {str(e)}")
    
    def inpaint(
        self,
        image_input: Union[str, Image.Image, bytes],
        mask_input: Union[str, Image.Image, bytes],
        prompt: str,
        style: Optional[str] = None,
        substyle: Optional[str] = None,
        random_seed: Optional[int] = None
    ) -> str:
        """
        Inpaint specific regions of an image based on a mask.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            mask_input: Mask image (path, PIL Image, or bytes)
            prompt: Text description for inpainting
            style: Image style
            substyle: Image substyle  
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the inpainted image
        """
        if not prompt or not prompt.strip():
            raise RecraftAPIError("Prompt is required for inpainting")
        
        image_data = self._make_image_data(image_input)
        mask_data = self._make_image_data(mask_input, is_mask=True)
        
        data = {
            'prompt': prompt.strip(),
            'style': style or Settings.DEFAULT_STYLE or None,
            'substyle': substyle or Settings.DEFAULT_SUBSTYLE or None,
            'random_seed': random_seed or None,
        }
        
        # Remove None values
        data = {k: v for k, v in data.items() if v is not None}
        
        files = {
            'image': io.BytesIO(image_data),
            'mask': io.BytesIO(mask_data)
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/images/inpaint",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Inpainting completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Inpainting API request failed: {str(e)}")
    
    def vectorize_image(
        self,
        image_input: Union[str, Image.Image, bytes],
        random_seed: Optional[int] = None
    ) -> str:
        """
        Convert a raster image to vector format.
        
        Args:
            image_input: Input image (path, PIL Image, or bytes)
            random_seed: Random seed for reproducible results
            
        Returns:
            str: URL of the vectorized image
        """
        image_data = self._make_image_data(image_input)
        
        data = {}
        if random_seed is not None:
            data['random_seed'] = random_seed
        
        files = {'image': io.BytesIO(image_data)}
        
        try:
            response = requests.post(
                f"{self.api_url}/images/vectorize",
                headers=self.headers,
                data=data,
                files=files,
                timeout=60
            )
            response.raise_for_status()
            
            result = self._handle_api_response(response)
            logger.info(f"Image vectorization completed: {result['url']}")
            return result['url']
            
        except requests.exceptions.RequestException as e:
            raise RecraftAPIError(f"Vectorization API request failed: {str(e)}")
    
    def test_connection(self) -> bool:
        """
        Test the API connection.
        
        Returns:
            bool: True if connection is successful
        """
        try:
            return self._validate_connection()
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def download_image(self, image_url: str, save_path: Optional[str] = None) -> str:
        """
        Download an image from a URL.
        
        Args:
            image_url: URL of the image to download
            save_path: Optional path to save the image
            
        Returns:
            str: Path where the image was saved
        """
        try:
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            # Generate save path if not provided
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"recraft_image_{timestamp}.png"
                save_path = os.path.join(Settings.GENERATED_IMAGES_DIR, filename)
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # Save the image
            with open(save_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"Image downloaded successfully: {save_path}")
            return save_path
            
        except Exception as e:
            raise RecraftAPIError(f"Failed to download image: {str(e)}")


# Add numpy import for mask processing
try:
    import numpy as np
except ImportError:
    logger.warning("NumPy not available, advanced mask processing disabled")
    np = None