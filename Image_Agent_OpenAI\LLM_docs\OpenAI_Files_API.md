
https://api.openai.com/v1/files

Upload a file that can be used across various endpoints. Individual files can be up to 512 MB, and the size of all files uploaded by one organization can be up to 100 GB.

The Assistants API supports files up to 2 million tokens and of specific file types. See the Assistants Tools guide for details.

The Fine-tuning API only supports .jsonl files. The input also has certain required formats for fine-tuning chat or completions models.

The Batch API only supports .jsonl files up to 200 MB in size. The input also has a specific required format.

Please contact us if you need to increase these storage limits.

Request body
file

file

Required

The File object (not file name) to be uploaded.

purpose

string

Required

The intended purpose of the uploaded file. One of: - assistants: Used in the Assistants API - batch: Used in the Batch API - fine-tune: Used for fine-tuning - vision: Images used for vision fine-tuning - user_data: Flexible file type for any purpose - evals: Used for eval data sets

Returns

The uploaded File object.

Example request
curl
1
2
3
4

curl https://api.openai.com/v1/files \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -F purpose="fine-tune" \
  -F file="@mydata.jsonl"
Response
1
2
3
4
5
6
7
8

{
  "id": "file-abc123",
  "object": "file",
  "bytes": 120000,
  "created_at": 1677610602,
  "filename": "mydata.jsonl",
  "purpose": "fine-tune",
}
List files
GET
 
https://api.openai.com/v1/files

Returns a list of files.

Query parameters
after

string

Optional

A cursor for use in pagination. after is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with obj_foo, your subsequent call can include after=obj_foo in order to fetch the next page of the list.

limit

integer

Optional
Defaults to 10000

A limit on the number of objects to be returned. Limit can range between 1 and 10,000, and the default is 10,000.

order

string

Optional
Defaults to desc

Sort order by the created_at timestamp of the objects. asc for ascending order and desc for descending order.

purpose

string

Optional

Only return files with the given purpose.

Returns

A list of File objects.

Example request
curl
1
2

curl https://api.openai.com/v1/files \
  -H "Authorization: Bearer $OPENAI_API_KEY"
Response
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21

{
  "data": [
    {
      "id": "file-abc123",
      "object": "file",
      "bytes": 175,
      "created_at": 1613677385,
      "filename": "salesOverview.pdf",
      "purpose": "assistants",
    },
    {
      "id": "file-abc123",
      "object": "file",
      "bytes": 140,
      "created_at": 1613779121,
      "filename": "puppy.jsonl",
      "purpose": "fine-tune",
    }
  ],
  "object": "list"
}
Retrieve file
GET
 
https://api.openai.com/v1/files/{file_id}

Returns information about a specific file.

Path parameters
file_id

string

Required

The ID of the file to use for this request.

Returns

The File object matching the specified ID.

Example request
curl
1
2

curl https://api.openai.com/v1/files/file-abc123 \
  -H "Authorization: Bearer $OPENAI_API_KEY"
Response
1
2
3
4
5
6
7
8

{
  "id": "file-abc123",
  "object": "file",
  "bytes": 120000,
  "created_at": 1677610602,
  "filename": "mydata.jsonl",
  "purpose": "fine-tune",
}
Delete file
DELETE
 
https://api.openai.com/v1/files/{file_id}

Delete a file.

Path parameters
file_id

string

Required

The ID of the file to use for this request.

Returns

Deletion status.

Example request
curl
1
2
3

curl https://api.openai.com/v1/files/file-abc123 \
  -X DELETE \
  -H "Authorization: Bearer $OPENAI_API_KEY"
Response
1
2
3
4
5

{
  "id": "file-abc123",
  "object": "file",
  "deleted": true
}
Retrieve file content
GET
 
https://api.openai.com/v1/files/{file_id}/content

Returns the contents of the specified file.

Path parameters
file_id

string

Required

The ID of the file to use for this request.

Returns

The file content.

Example request
curl
1
2

curl https://api.openai.com/v1/files/file-abc123/content \
  -H "Authorization: Bearer $OPENAI_API_KEY" > file.jsonl
The file object

The File object represents a document that has been uploaded to OpenAI.

bytes

integer

The size of the file, in bytes.

created_at

integer

The Unix timestamp (in seconds) for when the file was created.

expires_at

integer

The Unix timestamp (in seconds) for when the file will expire.

filename

string

The name of the file.

id

string

The file identifier, which can be referenced in the API endpoints.

object

string

The object type, which is always file.

purpose

string

The intended purpose of the file. Supported values are assistants, assistants_output, batch, batch_output, fine-tune, fine-tune-results, vision, and user_data.

status
Deprecated

string

Deprecated. The current status of the file, which can be either uploaded, processed, or error.

status_details
Deprecated

string

Deprecated. For details on why a fine-tuning training file failed validation, see the error field on fine_tuning.job.

OBJECT The file object
1
2
3
4
5
6
7
8
9

{
  "id": "file-abc123",
  "object": "file",
  "bytes": 120000,
  "created_at": 1677610602,
  "expires_at": 1680202602,
  "filename": "salesOverview.pdf",
  "purpose": "assistants",
}
Uploads

Allows you to upload large files in multiple parts.

Create upload
POST
 
https://api.openai.com/v1/uploads

Creates an intermediate Upload object that you can add Parts to. Currently, an Upload can accept at most 8 GB in total and expires after an hour after you create it.

Once you complete the Upload, we will create a File object that contains all the parts you uploaded. This File is usable in the rest of our platform as a regular File object.

For certain purpose values, the correct mime_type must be specified. Please refer to documentation for the supported MIME types for your use case.

For guidance on the proper filename extensions for each purpose, please follow the documentation on creating a File.

Request body
bytes

integer

Required

The number of bytes in the file you are uploading.

filename

string

Required

The name of the file to upload.

mime_type

string

Required

The MIME type of the file.

This must fall within the supported MIME types for your file purpose. See the supported MIME types for assistants and vision.

purpose

string

Required

The intended purpose of the uploaded file.

See the documentation on File purposes.

Returns

The Upload object with status pending.

Example request
curl
1
2
3
4
5
6
7
8

curl https://api.openai.com/v1/uploads \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -d '{
    "purpose": "fine-tune",
    "filename": "training_examples.jsonl",
    "bytes": 2147483648,
    "mime_type": "text/jsonl"
  }'
Response
1
2
3
4
5
6
7
8
9
10

{
  "id": "upload_abc123",
  "object": "upload",
  "bytes": 2147483648,
  "created_at": 1719184911,
  "filename": "training_examples.jsonl",
  "purpose": "fine-tune",
  "status": "pending",
  "expires_at": 1719127296
}
Add upload part
POST
 
https://api.openai.com/v1/uploads/{upload_id}/parts

Adds a Part to an Upload object. A Part represents a chunk of bytes from the file you are trying to upload.

Each Part can be at most 64 MB, and you can add Parts until you hit the Upload maximum of 8 GB.

It is possible to add multiple Parts in parallel. You can decide the intended order of the Parts when you complete the Upload.

Path parameters
upload_id

string

Required

The ID of the Upload.

Request body
data

file

Required

The chunk of bytes for this Part.

Returns

The upload Part object.

Example request
curl
1
2

curl https://api.openai.com/v1/uploads/upload_abc123/parts
  -F data="aHR0cHM6Ly9hcGkub3BlbmFpLmNvbS92MS91cGxvYWRz..."
Response
1
2
3
4
5
6

{
  "id": "part_def456",
  "object": "upload.part",
  "created_at": 1719185911,
  "upload_id": "upload_abc123"
}
Complete upload
POST
 
https://api.openai.com/v1/uploads/{upload_id}/complete

Completes the Upload.

Within the returned Upload object, there is a nested File object that is ready to use in the rest of the platform.

You can specify the order of the Parts by passing in an ordered list of the Part IDs.

The number of bytes uploaded upon completion must match the number of bytes initially specified when creating the Upload object. No Parts may be added after an Upload is completed.

Path parameters
upload_id

string

Required

The ID of the Upload.

Request body
part_ids

array

Required

The ordered list of Part IDs.

md5

string

Optional

The optional md5 checksum for the file contents to verify if the bytes uploaded matches what you expect.

Returns

The Upload object with status completed with an additional file property containing the created usable File object.

Example request
curl
1
2
3
4

curl https://api.openai.com/v1/uploads/upload_abc123/complete
  -d '{
    "part_ids": ["part_def456", "part_ghi789"]
  }'
Response
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18

{
  "id": "upload_abc123",
  "object": "upload",
  "bytes": 2147483648,
  "created_at": 1719184911,
  "filename": "training_examples.jsonl",
  "purpose": "fine-tune",
  "status": "completed",
  "expires_at": 1719127296,
  "file": {
    "id": "file-xyz321",
    "object": "file",
    "bytes": 2147483648,
    "created_at": 1719186911,
    "filename": "training_examples.jsonl",
    "purpose": "fine-tune",
  }
}
Cancel upload
POST
 
https://api.openai.com/v1/uploads/{upload_id}/cancel

Cancels the Upload. No Parts may be added after an Upload is cancelled.

Path parameters
upload_id

string

Required

The ID of the Upload.

Returns

The Upload object with status cancelled.

Example request
curl
curl https://api.openai.com/v1/uploads/upload_abc123/cancel
Response
1
2
3
4
5
6
7
8
9
10

{
  "id": "upload_abc123",
  "object": "upload",
  "bytes": 2147483648,
  "created_at": 1719184911,
  "filename": "training_examples.jsonl",
  "purpose": "fine-tune",
  "status": "cancelled",
  "expires_at": 1719127296
}
The upload object

The Upload object can accept byte chunks in the form of Parts.

bytes

integer

The intended number of bytes to be uploaded.

created_at

integer

The Unix timestamp (in seconds) for when the Upload was created.

expires_at

integer

The Unix timestamp (in seconds) for when the Upload will expire.

file

undefined or null

The ready File object after the Upload is completed.

filename

string

The name of the file to be uploaded.

id

string

The Upload unique identifier, which can be referenced in API endpoints.

object

string

The object type, which is always "upload".

purpose

string

The intended purpose of the file. Please refer here for acceptable values.

status

string

The status of the Upload.

OBJECT The upload object
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18

{
  "id": "upload_abc123",
  "object": "upload",
  "bytes": 2147483648,
  "created_at": 1719184911,
  "filename": "training_examples.jsonl",
  "purpose": "fine-tune",
  "status": "completed",
  "expires_at": 1719127296,
  "file": {
    "id": "file-xyz321",
    "object": "file",
    "bytes": 2147483648,
    "created_at": 1719186911,
    "filename": "training_examples.jsonl",
    "purpose": "fine-tune",
  }
}
The upload part object

The upload Part represents a chunk of bytes we can add to an Upload object.

created_at

integer

The Unix timestamp (in seconds) for when the Part was created.

id

string

The upload Part unique identifier, which can be referenced in API endpoints.

object

string

The object type, which is always upload.part.

upload_id

string

The ID of the Upload object that this Part was added to.

OBJECT The upload part object
1
2
3
4
5
6

{
    "id": "part_def456",
    "object": "upload.part",
    "created_at": 1719186911,
    "upload_id": "upload_abc123"
}
