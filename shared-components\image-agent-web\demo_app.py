"""
Multi-Provider Flask web application for Image Generation.
Provides a unified chat interface for multiple AI providers.
This is a test version with mock agents.
"""
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import json
from datetime import datetime
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Global storage
chat_history = []
custom_rulesets = {}

class MockAgent:
    """Mock agent for testing the frontend without actual AI calls."""
    
    def __init__(self, provider: str, model: str):
        self.provider = provider
        self.model = model
        self.custom_ruleset = None
    
    def set_custom_ruleset(self, ruleset: str):
        self.custom_ruleset = ruleset
    
    def generate(self, message: str, parameters: dict, images: list = None):
        """Generate mock response."""
        param_info = ", ".join([f"{k}={v}" for k, v in parameters.items() if v])
        response = f"🤖 **Mock Response from {self.provider.upper()} ({self.model})**\n\n"
        response += f"📝 Your prompt: \"{message[:100]}{'...' if len(message) > 100 else ''}\"\n\n"
        
        if param_info:
            response += f"🎛️ Parameters: {param_info}\n\n"
        
        if self.custom_ruleset:
            response += f"🤖 Custom ruleset applied: {self.custom_ruleset[:50]}...\n\n"
        
        if images:
            response += f"🖼️ {len(images)} reference image(s) processed\n\n"
        
        response += "✨ This is a demo response. In the full version, this would generate an actual image using the selected AI provider.\n\n"
        response += f"🚀 **Provider Capabilities:**\n"
        
        if self.provider == "openai":
            response += "• Advanced prompt optimization\n• Multi-turn editing\n• High-quality image generation"
        elif self.provider == "recraft":
            response += "• Professional vector illustrations\n• Precise style control\n• Commercial-grade quality"
        elif self.provider == "imagen":
            response += "• Photorealistic generation\n• Advanced understanding\n• High safety standards"
        elif self.provider == "flux":
            response += "• Open source flexibility\n• Customizable parameters\n• Fast generation"
        
        return {
            'response': response,
            'images': [],  # No actual images in mock mode
            'provider': self.provider,
            'model': self.model
        }
    
    def enhance_prompt(self, prompt: str, level: str = 'moderate'):
        """Mock prompt enhancement."""
        enhanced = f"Enhanced {prompt} with improved lighting, composition, and artistic style for {self.provider}"
        return {
            'enhanced_prompt': enhanced,
            'improvements': [
                'Added artistic style specifications',
                'Improved lighting descriptions', 
                'Enhanced composition details',
                f'Optimized for {self.provider} capabilities'
            ]
        }

def create_agent(provider: str, model: str) -> MockAgent:
    """Create a mock agent."""
    return MockAgent(provider, model)

@app.route('/')
def index():
    """Serve the main interface."""
    return render_template('chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat requests."""
    global chat_history, custom_rulesets
    
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        provider = data.get('provider', 'openai')
        model = data.get('model', 'gpt-image-1')
        
        # Extract all other parameters
        parameters = {k: v for k, v in data.items() 
                     if k not in ['message', 'provider', 'model', 'images'] and v != ''}
        uploaded_images = data.get('images', [])
        
        if not user_message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Create agent
        agent = create_agent(provider, model)
        
        # Apply custom ruleset if exists
        session_id = request.remote_addr
        if session_id in custom_rulesets:
            agent.set_custom_ruleset(custom_rulesets[session_id])
        
        # Generate response
        response_data = agent.generate(
            message=user_message,
            parameters=parameters,
            images=uploaded_images
        )
        
        # Add to chat history
        chat_history.append({
            'user': user_message,
            'agent': response_data.get('response', ''),
            'provider': provider,
            'model': model,
            'timestamp': datetime.now().isoformat()
        })
        
        return jsonify({
            'response': response_data.get('response', ''),
            'images': response_data.get('images', []),
            'provider': provider,
            'model': model,
            'processed_images_count': len(uploaded_images),
            'image_processing_errors': []
        })
        
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        return jsonify({'error': f'Error: {str(e)}'}), 500

@app.route('/api/enhance-prompt', methods=['POST'])
def enhance_prompt():
    """Handle prompt enhancement."""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '').strip()
        enhancement_level = data.get('enhancement_level', 'moderate')
        
        if not prompt:
            return jsonify({'error': 'Prompt is required', 'success': False}), 400
        
        # Use OpenAI agent for enhancement
        enhancer = create_agent('openai', 'gpt-4')
        enhanced_data = enhancer.enhance_prompt(prompt, enhancement_level)
        
        return jsonify({
            'success': True,
            'original_prompt': prompt,
            'enhanced_prompt': enhanced_data.get('enhanced_prompt', prompt),
            'improvements': enhanced_data.get('improvements', [])
        })
        
    except Exception as e:
        logger.error(f"Enhancement error: {str(e)}")
        return jsonify({'error': f'Enhancement failed: {str(e)}', 'success': False}), 500

@app.route('/api/set-custom-ruleset', methods=['POST'])
def set_custom_ruleset():
    """Set custom ruleset."""
    global custom_rulesets
    try:
        data = request.get_json()
        ruleset = data.get('ruleset', '').strip()
        session_id = request.remote_addr
        
        if not ruleset:
            return jsonify({'error': 'Ruleset is required'}), 400
        
        custom_rulesets[session_id] = ruleset
        return jsonify({'success': True, 'message': 'Custom ruleset applied'})
        
    except Exception as e:
        return jsonify({'error': f'Failed to set ruleset: {str(e)}'}), 500

@app.route('/api/clear-custom-ruleset', methods=['POST'])
def clear_custom_ruleset():
    """Clear custom ruleset."""
    global custom_rulesets
    try:
        session_id = request.remote_addr
        custom_rulesets.pop(session_id, None)
        return jsonify({'success': True, 'message': 'Custom ruleset cleared'})
    except Exception as e:
        return jsonify({'error': f'Failed to clear ruleset: {str(e)}'}), 500

@app.route('/api/clear', methods=['POST'])
def clear_history():
    """Clear chat history."""
    global chat_history
    try:
        chat_history = []
        return jsonify({'success': True, 'message': 'Chat history cleared'})
    except Exception as e:
        return jsonify({'error': f'Failed to clear history: {str(e)}'}), 500

@app.route('/api/images')
def get_images():
    """Get generated images list."""
    try:
        # Return empty list for now - would scan generated_images directory in full version
        return jsonify({'images': []})
    except Exception as e:
        return jsonify({'images': []})

@app.route('/health')
def health():
    """Health check."""
    return jsonify({
        'status': 'healthy',
        'providers': ['openai', 'recraft', 'imagen', 'flux'],
        'mode': 'demo',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting Multi-Provider Image Generation Web Interface")
    print("📍 Available providers: OpenAI, Recraft, Google Imagen, FLUX")
    print("🌐 Open http://localhost:5000 in your browser")
    print("⚠️  Note: This is currently a demo with mock agents")
    print("   Real image generation requires actual API keys and agent implementation")
    
    app.run(debug=True, host='0.0.0.0', port=5000)