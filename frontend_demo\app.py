"""
Simple demo server for Multi-Provider Image Agent frontend.
This demonstrates the frontend interface without requiring the full shared-components setup.
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import json
from pathlib import Path

app = Flask(__name__)
app.secret_key = 'demo-secret-key'

# Base directory
BASE_DIR = Path(__file__).parent

@app.route('/')
def index():
    """Serve the main interface."""
    return render_template('index.html')

@app.route('/api/generate', methods=['POST'])
def generate_image():
    """Mock image generation endpoint."""
    data = request.get_json()
    
    # Simulate processing time
    import time
    time.sleep(2)  # Remove in real implementation
    
    # Mock successful response
    response = {
        'status': 'success',
        'provider': data.get('provider', 'unknown'),
        'prompt': data.get('prompt', ''),
        'image_url': f"https://picsum.photos/{data.get('width', 512)}/{data.get('height', 512)}?random={time.time()}",
        'download_url': f"/download/generated_image_{int(time.time())}.jpg",
        'metadata': {
            'width': data.get('width', 512),
            'height': data.get('height', 512),
            'style': data.get('style', 'auto'),
            'quality': data.get('quality', 'standard')
        }
    }
    
    return jsonify(response)

@app.route('/api/providers')
def get_providers():
    """Get available providers."""
    providers = {
        'openai': {
            'name': 'OpenAI DALL-E',
            'description': 'Advanced AI art generation',
            'status': 'available',
            'icon': 'fas fa-brain'
        },
        'recraft': {
            'name': 'Recraft AI', 
            'description': 'Vector graphics specialist',
            'status': 'available',
            'icon': 'fas fa-vector-square'
        },
        'imagen': {
            'name': 'Google Imagen',
            'description': 'Photorealistic images',
            'status': 'coming_soon',
            'icon': 'fab fa-google'
        },
        'flux': {
            'name': 'FLUX.1',
            'description': 'Lightning fast generation',
            'status': 'coming_soon',
            'icon': 'fas fa-bolt'
        }
    }
    
    return jsonify(providers)

@app.route('/health')
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'version': '1.0.0',
        'providers': ['openai', 'recraft', 'imagen', 'flux']
    })

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files."""
    return send_from_directory(BASE_DIR / 'static', filename)

if __name__ == '__main__':
    print("Starting Multi-Provider Image Agent Demo")
    print("Open http://localhost:5000 in your browser")
    print("This is a frontend demo - actual AI generation requires API keys")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
