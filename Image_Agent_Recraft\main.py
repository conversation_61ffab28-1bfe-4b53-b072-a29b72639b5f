"""
Main application for the Recraft Image Generation Agent.
"""
import sys
import os
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage, AIMessage
from src.agent import RecraftAgent
from config.settings import Settings
import logging

# Load environment variables from .env file
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RecraftApp:
    """Main application class for the Recraft Image Generation Agent."""
    
    def __init__(self):
        """Initialize the application."""
        logger.info("Starting Recraft Image Generation Agent Application...")
        try:
            self.agent = RecraftAgent()
            self.chat_history = []
            logger.info("Application initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize application: {str(e)}")
            raise
    
    def add_to_history(self, user_message: str, agent_response: str):
        """Add messages to chat history."""
        self.chat_history.append(HumanMessage(content=user_message))
        self.chat_history.append(AIMessage(content=agent_response))
        
        # Keep only last 10 messages to avoid context length issues
        if len(self.chat_history) > 20:
            self.chat_history = self.chat_history[-20:]
    
    def run_interactive(self):
        """Interactive mode with Recraft-specific features."""
        print("\n" + "="*80)
        print("🎨 Recraft Vector Graphics & Design Agent")
        print("="*80)
        print("🚀 VECTOR GRAPHICS SPECIALIZATION:")
        print("  • Professional logo and icon generation")
        print("  • Scalable SVG and PDF output formats")
        print("  • Brand-consistent design assets")
        print("  • Advanced style and substyle combinations")
        print("\n🔧 RECRAFT FEATURES:")
        print("  • Multi-format output (PNG, SVG, PDF)")
        print("  • Professional design styles")
        print("  • Background processing and upscaling")
        print("  • Image-to-image transformations")
        print("  • Vectorization of existing images")
        print("\n📋 COMMANDS:")
        print("  generate [description]   - Create new images/graphics")
        print("  process [operation]      - Process existing images")
        print("  styles                   - Show available styles")
        print("  vector [description]     - Generate with vector output")
        print("  capabilities             - Show agent capabilities")
        print("  clear                    - Clear chat history")
        print("  test                     - Run system tests")
        print("  exit/quit               - End the session")
        print("\n💡 EXAMPLES:")
        print("  generate a modern company logo with blue and white colors")
        print("  vector create an icon set for a mobile app")
        print("  process remove_background from my_image.png")
        print("  styles list all available options")
        print("="*80)
        
        while True:
            try:
                user_input = input(f"\n🧑 You: ").strip()
                
                # Handle exit commands
                if user_input.lower() in ['exit', 'quit']:
                    print("\n👋 Goodbye! Thanks for using Recraft Agent!")
                    break
                
                # Handle clear chat history
                elif user_input.lower() == 'clear':
                    self.chat_history = []
                    print("\n🧹 Chat history cleared!")
                    continue
                
                # Handle system tests
                elif user_input.lower() == 'test':
                    print("\n🧪 Running system tests...")
                    self.run_tests()
                    continue
                
                # Handle capabilities query
                elif user_input.lower() == 'capabilities':
                    self._show_capabilities()
                    continue
                
                # Handle styles query
                elif user_input.lower() == 'styles':
                    self._handle_styles_query()
                    continue
                
                # Handle vector generation shortcut
                elif user_input.startswith('vector '):
                    prompt = user_input[7:]  # Remove 'vector ' prefix
                    enhanced_input = f"Generate an image with vector output: {prompt}. Set generate_vector=True and use appropriate style for scalable graphics."
                    user_input = enhanced_input
                
                # Handle generate command
                elif user_input.startswith('generate '):
                    prompt = user_input[9:]  # Remove 'generate ' prefix
                    enhanced_input = f"Use RecraftImageTool to generate: {prompt}"
                    user_input = enhanced_input
                
                # Handle process command
                elif user_input.startswith('process '):
                    instruction = user_input[8:]  # Remove 'process ' prefix
                    enhanced_input = f"Use RecraftProcessingTool for: {instruction}"
                    user_input = enhanced_input
                
                # Handle empty input
                elif not user_input:
                    print("Please enter a message or command.")
                    continue
                
                # Process the input
                print("\n🤖 Recraft Agent: ", end="", flush=True)
                response = self.agent.invoke(user_input, self.chat_history)
                print(response)
                
                # Add to history
                self.add_to_history(user_input, response)
                
            except KeyboardInterrupt:
                print("\n\n👋 Session interrupted. Goodbye!")
                break
            except Exception as e:
                logger.error(f"Error in interactive session: {str(e)}")
                print(f"\n❌ An error occurred: {str(e)}")
    
    def _show_capabilities(self):
        """Show agent capabilities."""
        try:
            capabilities = self.agent.get_capabilities()
            
            print("\n🔍 **RECRAFT AGENT CAPABILITIES**")
            print("-" * 50)
            print(f"Name: {capabilities.get('name', 'Unknown')}")
            print(f"Version: {capabilities.get('version', 'Unknown')}")
            
            caps = capabilities.get('capabilities', {})
            print(f"\nCore Features:")
            print(f"  • Image Generation: {'✅' if caps.get('image_generation') else '❌'}")
            print(f"  • Vector Graphics: {'✅' if caps.get('vector_graphics') else '❌'}")
            print(f"  • Image Processing: {'✅' if caps.get('image_processing') else '❌'}")
            print(f"  • Style Guidance: {'✅' if caps.get('style_guidance') else '❌'}")
            print(f"  • Multi-Format Output: {'✅' if caps.get('multi_format_output') else '❌'}")
            
            status = capabilities.get('status', {})
            print(f"\nSystem Status:")
            print(f"  • LLM Ready: {'✅' if status.get('llm_ready') else '❌'}")
            print(f"  • Recraft API Ready: {'✅' if status.get('recraft_ready') else '❌'}")
            print(f"  • Vector Processing Ready: {'✅' if status.get('vector_ready') else '❌'}")
            
            formats = capabilities.get('supported_formats', {})
            if formats.get('input'):
                print(f"\nSupported Input Formats: {', '.join(formats['input'])}")
            if formats.get('vector_output'):
                print(f"Vector Output Formats: {', '.join(formats['vector_output'])}")
            
            tools = capabilities.get('tools', [])
            if tools:
                print(f"\nAvailable Tools: {', '.join(tools)}")
                
        except Exception as e:
            print(f"❌ Error getting capabilities: {str(e)}")
    
    def _handle_styles_query(self):
        """Handle styles information request."""
        try:
            # Query the style tool
            response = self.agent.invoke("Show me all available Recraft styles and substyles")
            print(f"\n{response}")
        except Exception as e:
            print(f"❌ Error getting styles: {str(e)}")
    
    def run_single_query(self, query: str) -> str:
        """
        Run a single query and return the response.
        
        Args:
            query: The user's query
            
        Returns:
            str: The agent's response
        """
        try:
            response = self.agent.invoke(query, self.chat_history)
            self.add_to_history(query, response)
            return response
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def run_tests(self):
        """Run comprehensive system tests."""
        print("\n🔍 Running comprehensive tests...")
        
        tests = [
            ("Configuration", self._test_configuration),
            ("LLM Integration", self._test_llm),
            ("Recraft API", self._test_recraft_api),
            ("Vector Processing", self._test_vector_processing),
            ("Agent Functionality", self._test_agent),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                print(f"\n  Testing {test_name}...", end=" ")
                result = test_func()
                if result:
                    print("✓ PASSED")
                    results.append(True)
                else:
                    print("✗ FAILED")
                    results.append(False)
            except Exception as e:
                print(f"✗ ERROR: {str(e)}")
                results.append(False)
        
        # Summary
        passed = sum(results)
        total = len(results)
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! The Recraft agent is ready to use.")
        else:
            print("⚠️  Some tests failed. Please check the configuration and try again.")
    
    def _test_configuration(self) -> bool:
        """Test configuration settings."""
        return Settings.validate()
    
    def _test_llm(self) -> bool:
        """Test LLM integration."""
        return self.agent.llm_integration.test_connectivity()
    
    def _test_recraft_api(self) -> bool:
        """Test Recraft API connection."""
        return self.agent._test_recraft_connection()
    
    def _test_vector_processing(self) -> bool:
        """Test vector processing."""
        return self.agent._test_vector_processing()
    
    def _test_agent(self) -> bool:
        """Test agent functionality."""
        return self.agent.test_agent()


def main():
    """Main entry point."""
    try:
        # Check if running with arguments
        if len(sys.argv) > 1:
            if sys.argv[1] == "test":
                app = RecraftApp()
                app.run_tests()
            elif sys.argv[1] == "capabilities":
                app = RecraftApp()
                app._show_capabilities()
            else:
                # Run with a single query
                query = " ".join(sys.argv[1:])
                app = RecraftApp()
                response = app.run_single_query(query)
                print(f"\nQuery: {query}")
                print(f"Response: {response}")
        else:
            # Run interactive mode
            app = RecraftApp()
            app.run_interactive()
            
    except Exception as e:
        logger.error(f"Application failed to start: {str(e)}")
        print(f"\n❌ Failed to start application: {str(e)}")
        print("\nPlease check:")
        print("1. Your Recraft API key is set in the .env file")
        print("2. Your OpenAI API key is set for LLM functionality")
        print("3. All required packages are installed")
        print("4. You have internet connectivity")
        
        # Show specific configuration issues
        try:
            Settings.validate()
        except Exception as config_error:
            print(f"5. Configuration error: {config_error}")
        
        sys.exit(1)


if __name__ == "__main__":
    main()