"""
CLI framework for Image Agent providers.
Provides common command-line interface patterns and utilities.
"""
import sys
import os
import argparse
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import asyncio
from abc import ABC, abstractmethod

# Add the parent directory to sys.path to import shared components
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'image-agent-core'))

try:
    from src.file_manager import FileManager, ConfigManager
    from src.validation import InputValidator, SecurityValidator
    from src.image_encoder import ImageEncoder
    from src.base_settings import BaseSettings, LoggingConfig
except ImportError:
    # Fallback for development
    print("Warning: Could not import shared components")
    FileManager = None
    ConfigManager = None

logger = logging.getLogger(__name__)


class BaseCLI(ABC):
    """Base CLI class for Image Agent providers."""
    
    def __init__(self, provider_name: str, version: str = "1.0.0"):
        """
        Initialize CLI application.
        
        Args:
            provider_name: Name of the AI provider
            version: Application version
        """
        self.provider_name = provider_name
        self.version = version
        
        # Initialize components if available
        if FileManager:
            self.file_manager = FileManager()
            self.config_manager = ConfigManager()
            self.validator = InputValidator()
            self.security_validator = SecurityValidator()
            self.image_encoder = ImageEncoder()
        
        # CLI state
        self.args = None
        self.config = {}
        
        # Set up argument parser
        self.parser = self._create_parser()
        self._add_common_arguments()
        self._add_provider_arguments()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """Create the main argument parser."""
        return argparse.ArgumentParser(
            prog=f"{self.provider_name.lower()}-agent",
            description=f"Command-line interface for {self.provider_name} Image Agent",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog=f"""
Examples:
  {self.provider_name.lower()}-agent generate "A cat wearing a hat"
  {self.provider_name.lower()}-agent generate "Sunset over mountains" --width 1024 --height 768
  {self.provider_name.lower()}-agent config --set api_key YOUR_API_KEY
  {self.provider_name.lower()}-agent history --limit 10
            """
        )
    
    def _add_common_arguments(self):
        """Add common CLI arguments."""
        # Version
        self.parser.add_argument(
            '--version', 
            action='version', 
            version=f'{self.provider_name} Image Agent v{self.version}'
        )
        
        # Verbosity
        self.parser.add_argument(
            '-v', '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
        
        self.parser.add_argument(
            '--debug',
            action='store_true',
            help='Enable debug mode'
        )
        
        # Configuration
        self.parser.add_argument(
            '--config',
            type=str,
            help='Path to configuration file'
        )
        
        # Output directory
        self.parser.add_argument(
            '--output-dir',
            type=str,
            default='./generated_images',
            help='Directory to save generated images (default: ./generated_images)'
        )
        
        # Create subparsers
        subparsers = self.parser.add_subparsers(dest='command', help='Available commands')
        
        # Generate command
        generate_parser = subparsers.add_parser('generate', help='Generate an image')
        self._add_generate_arguments(generate_parser)
        
        # Config command
        config_parser = subparsers.add_parser('config', help='Manage configuration')
        self._add_config_arguments(config_parser)
        
        # History command
        history_parser = subparsers.add_parser('history', help='View generation history')
        self._add_history_arguments(history_parser)
        
        # Status command
        status_parser = subparsers.add_parser('status', help='Check system status')
        self._add_status_arguments(status_parser)
    
    def _add_generate_arguments(self, parser):
        """Add arguments for the generate command."""
        parser.add_argument(
            'prompt',
            type=str,
            help='Text description of the image to generate'
        )
        
        parser.add_argument(
            '--width',
            type=int,
            default=512,
            help='Image width in pixels (default: 512)'
        )
        
        parser.add_argument(
            '--height', 
            type=int,
            default=512,
            help='Image height in pixels (default: 512)'
        )
        
        parser.add_argument(
            '--style',
            type=str,
            choices=['auto', 'realistic', 'artistic', 'cartoon', 'abstract'],
            default='auto',
            help='Image style (default: auto)'
        )
        
        parser.add_argument(
            '--quality',
            type=str,
            choices=['standard', 'high', 'ultra'],
            default='standard',
            help='Image quality (default: standard)'
        )
        
        parser.add_argument(
            '--output',
            type=str,
            help='Output filename (optional)'
        )
        
        parser.add_argument(
            '--format',
            type=str,
            choices=['png', 'jpg', 'jpeg', 'webp'],
            default='png',
            help='Output format (default: png)'
        )
        
        parser.add_argument(
            '--seed',
            type=int,
            help='Random seed for reproducible results'
        )
        
        parser.add_argument(
            '--wait',
            action='store_true',
            help='Wait for generation to complete before returning'
        )
        
        parser.add_argument(
            '--preview',
            action='store_true',
            help='Show image preview in terminal (if supported)'
        )
    
    def _add_config_arguments(self, parser):
        """Add arguments for the config command."""
        config_group = parser.add_mutually_exclusive_group(required=True)
        
        config_group.add_argument(
            '--set',
            nargs=2,
            metavar=('KEY', 'VALUE'),
            help='Set a configuration value'
        )
        
        config_group.add_argument(
            '--get',
            type=str,
            metavar='KEY',
            help='Get a configuration value'
        )
        
        config_group.add_argument(
            '--list',
            action='store_true',
            help='List all configuration values'
        )
        
        config_group.add_argument(
            '--reset',
            action='store_true',
            help='Reset configuration to defaults'
        )
        
        config_group.add_argument(
            '--validate',
            action='store_true',
            help='Validate current configuration'
        )
    
    def _add_history_arguments(self, parser):
        """Add arguments for the history command."""
        parser.add_argument(
            '--limit',
            type=int,
            default=10,
            help='Number of history items to show (default: 10)'
        )
        
        parser.add_argument(
            '--format',
            type=str,
            choices=['table', 'json', 'csv'],
            default='table',
            help='Output format (default: table)'
        )
        
        parser.add_argument(
            '--filter',
            type=str,
            help='Filter history by prompt text'
        )
        
        parser.add_argument(
            '--since',
            type=str,
            help='Show history since date (YYYY-MM-DD)'
        )
    
    def _add_status_arguments(self, parser):
        """Add arguments for the status command."""
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed status information'
        )
        
        parser.add_argument(
            '--json',
            action='store_true',
            help='Output status in JSON format'
        )
    
    @abstractmethod
    def _add_provider_arguments(self):
        """Add provider-specific CLI arguments. Override in subclasses."""
        pass
    
    def run(self, argv: List[str] = None) -> int:
        """
        Run the CLI application.
        
        Args:
            argv: Command line arguments (defaults to sys.argv[1:])
            
        Returns:
            Exit code (0 for success, non-zero for error)
        """
        try:
            # Parse arguments
            self.args = self.parser.parse_args(argv)
            
            # Set up logging
            self._setup_logging()
            
            # Load configuration
            self._load_configuration()
            
            # Validate configuration
            if self.args.command != 'config':
                self._validate_configuration()
            
            # Execute command
            return self._execute_command()
            
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 130
        except Exception as e:
            if self.args and self.args.debug:
                import traceback
                traceback.print_exc()
            else:
                print(f"Error: {e}")
            return 1
    
    def _setup_logging(self):
        """Set up logging configuration."""
        log_level = 'DEBUG' if self.args.debug else ('INFO' if self.args.verbose else 'WARNING')
        
        if LoggingConfig:
            LoggingConfig.setup_logging(
                log_level=log_level,
                format_string='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        else:
            logging.basicConfig(
                level=getattr(logging, log_level),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
    
    def _load_configuration(self):
        """Load configuration from file and environment."""
        if self.config_manager:
            config_file = self.args.config or 'config.json'
            try:
                self.config = self.config_manager.load_config(config_file)
            except FileNotFoundError:
                self.config = {}
                logger.debug(f"Configuration file {config_file} not found, using defaults")
        else:
            self.config = {}
        
        # Override with environment variables
        self._load_env_config()
    
    def _load_env_config(self):
        """Load configuration from environment variables."""
        env_mapping = {
            'api_key': f'{self.provider_name.upper()}_API_KEY',
            'api_base': f'{self.provider_name.upper()}_API_BASE',
            'timeout': f'{self.provider_name.upper()}_TIMEOUT',
            'max_retries': f'{self.provider_name.upper()}_MAX_RETRIES',
        }
        
        for config_key, env_key in env_mapping.items():
            value = os.getenv(env_key)
            if value:
                self.config[config_key] = value
    
    def _validate_configuration(self):
        """Validate the current configuration."""
        required_keys = ['api_key']
        missing_keys = []
        
        for key in required_keys:
            if key not in self.config or not self.config[key]:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"Missing required configuration: {', '.join(missing_keys)}")
            print(f"Use '{self.provider_name.lower()}-agent config --set <key> <value>' to set values")
            print(f"Or set environment variables: {', '.join(f'{self.provider_name.upper()}_{k.upper()}' for k in missing_keys)}")
            return False
        
        return True
    
    def _execute_command(self) -> int:
        """Execute the specified command."""
        command = self.args.command
        
        if command == 'generate':
            return self._handle_generate()
        elif command == 'config':
            return self._handle_config()
        elif command == 'history':
            return self._handle_history()
        elif command == 'status':
            return self._handle_status()
        else:
            self.parser.print_help()
            return 1
    
    def _handle_generate(self) -> int:
        """Handle the generate command."""
        try:
            # Validate inputs
            if self.validator:
                self.validator.validate_prompt(self.args.prompt)
                self.validator.validate_image_size(self.args.width, self.args.height)
            
            # Prepare generation parameters
            params = {
                'prompt': self.args.prompt,
                'width': self.args.width,
                'height': self.args.height,
                'style': self.args.style,
                'quality': self.args.quality,
                'format': self.args.format,
            }
            
            if self.args.seed:
                params['seed'] = self.args.seed
            
            print(f"Generating image with {self.provider_name}...")
            print(f"Prompt: {self.args.prompt}")
            print(f"Size: {self.args.width}x{self.args.height}")
            
            # Call provider-specific generation
            result = self._generate_image(params)
            
            if result:
                print(f"✓ Image generated successfully!")
                if 'file_path' in result:
                    print(f"  Saved to: {result['file_path']}")
                if 'url' in result:
                    print(f"  URL: {result['url']}")
                
                # Show preview if requested
                if self.args.preview and 'file_path' in result:
                    self._show_preview(result['file_path'])
                
                return 0
            else:
                print("✗ Image generation failed")
                return 1
                
        except Exception as e:
            print(f"✗ Generation failed: {e}")
            if self.args.debug:
                import traceback
                traceback.print_exc()
            return 1
    
    def _handle_config(self) -> int:
        """Handle the config command."""
        if self.args.set:
            key, value = self.args.set
            self.config[key] = value
            if self.config_manager:
                self.config_manager.save_config(self.config)
            print(f"✓ Set {key} = {value}")
            return 0
            
        elif self.args.get:
            key = self.args.get
            value = self.config.get(key, 'Not set')
            print(f"{key} = {value}")
            return 0
            
        elif self.args.list:
            print("Configuration:")
            for key, value in self.config.items():
                # Mask sensitive values
                if 'key' in key.lower() or 'token' in key.lower():
                    display_value = '***' if value else 'Not set'
                else:
                    display_value = value
                print(f"  {key} = {display_value}")
            return 0
            
        elif self.args.reset:
            self.config = {}
            if self.config_manager:
                self.config_manager.save_config(self.config)
            print("✓ Configuration reset to defaults")
            return 0
            
        elif self.args.validate:
            if self._validate_configuration():
                print("✓ Configuration is valid")
                return 0
            else:
                print("✗ Configuration is invalid")
                return 1
        
        return 1
    
    def _handle_history(self) -> int:
        """Handle the history command."""
        # This would be implemented by each provider
        history = self._get_history(limit=self.args.limit, filter_text=self.args.filter)
        
        if not history:
            print("No generation history found")
            return 0
        
        if self.args.format == 'json':
            print(json.dumps(history, indent=2))
        elif self.args.format == 'csv':
            self._print_history_csv(history)
        else:
            self._print_history_table(history)
        
        return 0
    
    def _handle_status(self) -> int:
        """Handle the status command."""
        status = self._get_system_status()
        
        if self.args.json:
            print(json.dumps(status, indent=2))
        else:
            self._print_status_table(status)
        
        return 0
    
    def _print_history_table(self, history: List[Dict[str, Any]]):
        """Print history in table format."""
        if not history:
            return
        
        # Table headers
        headers = ['Date', 'Status', 'Prompt', 'Size', 'File']
        
        # Calculate column widths
        widths = [len(h) for h in headers]
        for item in history:
            widths[0] = max(widths[0], len(item.get('created_at', '')[:19]))
            widths[1] = max(widths[1], len(item.get('status', '')))
            widths[2] = max(widths[2], min(50, len(item.get('prompt', ''))))
            widths[3] = max(widths[3], len(f"{item.get('width', 0)}x{item.get('height', 0)}"))
            widths[4] = max(widths[4], len(Path(item.get('file_path', '')).name))
        
        # Print table
        separator = '+' + '+'.join('-' * (w + 2) for w in widths) + '+'
        header_row = '|' + '|'.join(f' {h:<{w}} ' for h, w in zip(headers, widths)) + '|'
        
        print(separator)
        print(header_row)
        print(separator)
        
        for item in history:
            date = item.get('created_at', '')[:19]
            status = item.get('status', '')
            prompt = item.get('prompt', '')[:47] + '...' if len(item.get('prompt', '')) > 50 else item.get('prompt', '')
            size = f"{item.get('width', 0)}x{item.get('height', 0)}"
            file = Path(item.get('file_path', '')).name
            
            row = '|' + '|'.join(f' {v:<{w}} ' for v, w in zip([date, status, prompt, size, file], widths)) + '|'
            print(row)
        
        print(separator)
    
    def _print_history_csv(self, history: List[Dict[str, Any]]):
        """Print history in CSV format."""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=['created_at', 'status', 'prompt', 'width', 'height', 'file_path'])
        writer.writeheader()
        writer.writerows(history)
        print(output.getvalue().strip())
    
    def _print_status_table(self, status: Dict[str, Any]):
        """Print status in table format."""
        print(f"\n{self.provider_name} Image Agent Status")
        print("=" * 40)
        
        for key, value in status.items():
            if isinstance(value, dict):
                print(f"\n{key.title()}:")
                for sub_key, sub_value in value.items():
                    print(f"  {sub_key}: {sub_value}")
            else:
                print(f"{key.title()}: {value}")
        print()
    
    def _show_preview(self, file_path: str):
        """Show image preview in terminal (if supported)."""
        try:
            # Try to use common terminal image viewers
            import subprocess
            
            viewers = ['imgcat', 'catimg', 'tiv']
            
            for viewer in viewers:
                try:
                    subprocess.run([viewer, file_path], check=True, capture_output=True)
                    return
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
            
            print(f"  Preview not available (install imgcat, catimg, or tiv for terminal preview)")
            
        except Exception as e:
            logger.debug(f"Preview failed: {e}")
    
    # Abstract methods to be implemented by providers
    @abstractmethod
    def _generate_image(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Generate an image with the provider.
        
        Args:
            params: Generation parameters
            
        Returns:
            Result dictionary or None on failure
        """
        pass
    
    @abstractmethod
    def _get_history(self, limit: int = 10, filter_text: str = None) -> List[Dict[str, Any]]:
        """
        Get generation history.
        
        Args:
            limit: Maximum number of items to return
            filter_text: Optional text filter
            
        Returns:
            List of history items
        """
        pass
    
    @abstractmethod
    def _get_system_status(self) -> Dict[str, Any]:
        """
        Get system status.
        
        Returns:
            Status dictionary
        """
        pass


class AsyncBaseCLI(BaseCLI):
    """Async version of BaseCLI for providers that use async operations."""
    
    def run(self, argv: List[str] = None) -> int:
        """Run the CLI application with async support."""
        try:
            # Parse arguments
            self.args = self.parser.parse_args(argv)
            
            # Set up logging
            self._setup_logging()
            
            # Load configuration
            self._load_configuration()
            
            # Validate configuration
            if self.args.command != 'config':
                self._validate_configuration()
            
            # Execute command with async support
            return asyncio.run(self._execute_command_async())
            
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 130
        except Exception as e:
            if self.args and self.args.debug:
                import traceback
                traceback.print_exc()
            else:
                print(f"Error: {e}")
            return 1
    
    async def _execute_command_async(self) -> int:
        """Execute the specified command with async support."""
        command = self.args.command
        
        if command == 'generate':
            return await self._handle_generate_async()
        elif command == 'config':
            return self._handle_config()
        elif command == 'history':
            return await self._handle_history_async()
        elif command == 'status':
            return await self._handle_status_async()
        else:
            self.parser.print_help()
            return 1
    
    async def _handle_generate_async(self) -> int:
        """Handle the generate command with async support."""
        try:
            # Validate inputs
            if self.validator:
                self.validator.validate_prompt(self.args.prompt)
                self.validator.validate_image_size(self.args.width, self.args.height)
            
            # Prepare generation parameters
            params = {
                'prompt': self.args.prompt,
                'width': self.args.width,
                'height': self.args.height,
                'style': self.args.style,
                'quality': self.args.quality,
                'format': self.args.format,
            }
            
            if self.args.seed:
                params['seed'] = self.args.seed
            
            print(f"Generating image with {self.provider_name}...")
            print(f"Prompt: {self.args.prompt}")
            print(f"Size: {self.args.width}x{self.args.height}")
            
            # Call provider-specific generation
            result = await self._generate_image_async(params)
            
            if result:
                print(f"✓ Image generated successfully!")
                if 'file_path' in result:
                    print(f"  Saved to: {result['file_path']}")
                if 'url' in result:
                    print(f"  URL: {result['url']}")
                
                # Show preview if requested
                if self.args.preview and 'file_path' in result:
                    self._show_preview(result['file_path'])
                
                return 0
            else:
                print("✗ Image generation failed")
                return 1
                
        except Exception as e:
            print(f"✗ Generation failed: {e}")
            if self.args.debug:
                import traceback
                traceback.print_exc()
            return 1
    
    async def _handle_history_async(self) -> int:
        """Handle the history command with async support."""
        history = await self._get_history_async(limit=self.args.limit, filter_text=self.args.filter)
        
        if not history:
            print("No generation history found")
            return 0
        
        if self.args.format == 'json':
            print(json.dumps(history, indent=2))
        elif self.args.format == 'csv':
            self._print_history_csv(history)
        else:
            self._print_history_table(history)
        
        return 0
    
    async def _handle_status_async(self) -> int:
        """Handle the status command with async support."""
        status = await self._get_system_status_async()
        
        if self.args.json:
            print(json.dumps(status, indent=2))
        else:
            self._print_status_table(status)
        
        return 0
    
    # Async abstract methods
    @abstractmethod
    async def _generate_image_async(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Async version of _generate_image."""
        pass
    
    @abstractmethod
    async def _get_history_async(self, limit: int = 10, filter_text: str = None) -> List[Dict[str, Any]]:
        """Async version of _get_history."""
        pass
    
    @abstractmethod
    async def _get_system_status_async(self) -> Dict[str, Any]:
        """Async version of _get_system_status."""
        pass


def create_cli_entry_point(cli_class, provider_name: str) -> Callable:
    """
    Create an entry point function for a CLI class.
    
    Args:
        cli_class: CLI class to instantiate
        provider_name: Provider name
        
    Returns:
        Entry point function
    """
    def entry_point():
        cli = cli_class(provider_name)
        sys.exit(cli.run())
    
    return entry_point