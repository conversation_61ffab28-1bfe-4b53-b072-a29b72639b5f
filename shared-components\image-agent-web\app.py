""""""

Multi-Provider Flask web application for Image Generation.Multi-Provider Flask web application for Image Generation.

Provides a unified chat interface for interacting with multiple AI image generation providers:Provides a unified chat interface for interacting with multiple AI image generation providers:

- OpenAI (gpt-image-1)- OpenAI (gpt-image-1)

- Recraft V3- Recraft V3

- Google Imagen 3- Google Imagen 3

- FLUX Pro- FLUX Pro

""""""

from flask import Flask, render_template, request, jsonify, send_from_directoryfrom flask import Flask, render_template, request, jsonify, send_from_directory

from flask_cors import CORSfrom flask_cors import CORS

import osimport os

import base64import base64

import jsonimport json

import sysimport sys

from datetime import datetimefrom datetime import datetime

from dotenv import load_dotenvfrom dotenv import load_dotenv

import loggingimport logging

from typing import Dict, Any, Optionalfrom typing import Dict, Any, Optional



# Load environment variables from .env file# Add the parent directory to Python path to import shared components

load_dotenv()sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))



# Setup logging# Import shared components

logging.basicConfig(level=logging.INFO)from image_agent_core.agent_factory import AgentFactory

logger = logging.getLogger(__name__)from image_agent_core.providers import ProviderType

from image_agent_web.utils.parameter_mapper import ParameterMapper

app = Flask(__name__)from image_agent_web.utils.response_formatter import ResponseFormatter

CORS(app)

# Load environment variables from .env file

# Global instancesload_dotenv()

chat_history = []

custom_rulesets = {}# Setup logging

logging.basicConfig(level=logging.INFO)

# Simple agent mock for now - will be replaced with actual shared componentslogger = logging.getLogger(__name__)

class MultiProviderAgent:

    """Temporary mock agent for testing the frontend."""app = Flask(__name__)

    CORS(app)

    def __init__(self, provider: str, model: str):

        self.provider = provider# Global instances

        self.model = modelagent_factory = None

        self.custom_ruleset = Nonechat_history = []

    custom_rulesets = {}

    def set_custom_ruleset(self, ruleset: str):

        """Set custom ruleset for this agent."""def initialize_agent_factory():

        self.custom_ruleset = ruleset    """Initialize the agent factory for multi-provider support."""

        global agent_factory

    def generate(self, message: str, parameters: dict, images: list = None):    try:

        """Generate response - mock implementation."""        agent_factory = AgentFactory()

        return {        logger.info("Agent factory initialized successfully")

            'response': f'Mock response from {self.provider} ({self.model}): {message[:50]}...',        return True

            'images': [],    except Exception as e:

            'metadata': {        logger.error(f"Failed to initialize agent factory: {str(e)}")

                'provider': self.provider,        return False

                'model': self.model,

                'parameters': <EMAIL>('/')

            }def index():

        }    """Serve the main multi-provider chat interface."""

        return render_template('chat.html')

    def enhance_prompt(self, prompt: str, level: str = 'moderate'):

        """Enhance prompt - mock implementation."""@app.route('/api/chat', methods=['POST'])

        return {def chat():

            'enhanced_prompt': f"Enhanced: {prompt}",    """Handle chat messages with multi-provider routing."""

            'improvements': ['Added detail', 'Improved clarity', 'Better structure']    global agent_factory, chat_history, custom_rulesets

        }

        if not agent_factory:

    def process_image(self, image_data: dict):        if not initialize_agent_factory():

        """Process image - mock implementation."""            return jsonify({

        return {                'error': 'Agent factory initialization failed. Please check your API keys and try again.'

            'processed': True,            }), 500

            'name': image_data.get('name', 'unknown'),

            'size': len(image_data.get('base64', ''))    try:

        }        data = request.get_json()

        user_message = data.get('message', '').strip()

def create_agent(provider: str, model: str) -> MultiProviderAgent:        provider = data.get('provider', 'openai')

    """Create an agent for the specified provider and model."""        model = data.get('model', 'gpt-image-1')

    return MultiProviderAgent(provider, model)        parameters = data.get('parameters', {})

        uploaded_images = data.get('images', [])

@app.route('/')        

def index():        if not user_message:

    """Serve the main multi-provider chat interface."""            return jsonify({'error': 'Message is required'}), 400

    return render_template('chat.html')        

        # Map provider string to ProviderType enum

@app.route('/api/chat', methods=['POST'])        provider_type_map = {

def chat():            'openai': ProviderType.OPENAI,

    """Handle chat messages with multi-provider routing."""            'recraft': ProviderType.RECRAFT,

    global chat_history, custom_rulesets            'imagen': ProviderType.IMAGEN,

            'flux': ProviderType.FLUX

    try:        }

        data = request.get_json()        

        user_message = data.get('message', '').strip()        provider_type = provider_type_map.get(provider)

        provider = data.get('provider', 'openai')        if not provider_type:

        model = data.get('model', 'gpt-image-1')            return jsonify({'error': f'Unsupported provider: {provider}'}), 400

        parameters = {k: v for k, v in data.items() if k not in ['message', 'provider', 'model', 'images']}

        uploaded_images = data.get('images', [])        # Get or create agent for the specific provider

                agent = agent_factory.create_agent(provider_type, model)

        if not user_message:        if not agent:

            return jsonify({'error': 'Message is required'}), 400            return jsonify({'error': f'Failed to create agent for provider: {provider}'}), 500

        

        # Create agent for the specific provider        # Map parameters to provider-specific format

        agent = create_agent(provider, model)        mapper = ParameterMapper()

        mapped_params = mapper.map_parameters(provider_type, parameters)

        # Apply custom ruleset if exists

        session_id = request.remote_addr        # Apply custom ruleset if exists

        if session_id in custom_rulesets:        session_id = request.remote_addr

            ruleset = custom_rulesets[session_id]        if session_id in custom_rulesets:

            agent.set_custom_ruleset(ruleset)            ruleset = custom_rulesets[session_id]

            agent.set_custom_ruleset(ruleset)

        # Process uploaded images

        processed_images = []        # Process uploaded images

        image_processing_errors = []        processed_images = []

                image_processing_errors = []

        if uploaded_images:        

            for img_data in uploaded_images:        if uploaded_images:

                try:            for img_data in uploaded_images:

                    processed_img = agent.process_image(img_data)                try:

                    processed_images.append(processed_img)                    # Process image data for the specific provider

                except Exception as e:                    processed_img = agent.process_image(img_data)

                    error_msg = f"Failed to process image {img_data.get('name', 'unknown')}: {str(e)}"                    processed_images.append(processed_img)

                    image_processing_errors.append(error_msg)                except Exception as e:

                    logger.warning(error_msg)                    error_msg = f"Failed to process image {img_data.get('name', 'unknown')}: {str(e)}"

                    image_processing_errors.append(error_msg)

        # Generate response using the specific provider                    logger.warning(error_msg)

        try:

            response_data = agent.generate(        # Generate response using the specific provider

                message=user_message,        try:

                parameters=parameters,            response_data = agent.generate(

                images=processed_images                message=user_message,

            )                parameters=mapped_params,

                            images=processed_images

            # Add response to chat history            )

            chat_history.append({            

                'user': user_message,            # Format response consistently across providers

                'agent': response_data.get('response', ''),            formatter = ResponseFormatter()

                'provider': provider,            formatted_response = formatter.format_response(provider_type, response_data)

                'model': model,            

                'timestamp': datetime.now().isoformat(),            # Add response to chat history

                'images': response_data.get('images', [])            chat_history.append({

            })                'user': user_message,

                            'agent': formatted_response.get('response', ''),

            # Return response with additional metadata                'provider': provider,

            result = {                'model': model,

                'response': response_data.get('response', ''),                'timestamp': datetime.now().isoformat(),

                'images': response_data.get('images', []),                'images': formatted_response.get('images', [])

                'provider': provider,            })

                'model': model,            

                'processed_images_count': len(processed_images),            # Return response with additional metadata

                'image_processing_errors': image_processing_errors            result = {

            }                'response': formatted_response.get('response', ''),

                            'images': formatted_response.get('images', []),

            return jsonify(result)                'provider': provider,

                            'model': model,

        except Exception as e:                'processed_images_count': len(processed_images),

            error_msg = f"Image generation failed with {provider}: {str(e)}"                'image_processing_errors': image_processing_errors

            logger.error(error_msg)            }

            return jsonify({'error': error_msg}), 500            

                        return jsonify(result)

    except Exception as e:            

        logger.error(f"Chat endpoint error: {str(e)}")        except Exception as e:

        return jsonify({'error': f'Internal server error: {str(e)}'}), 500            error_msg = f"Image generation failed with {provider}: {str(e)}"

            logger.error(error_msg)

@app.route('/api/enhance-prompt', methods=['POST'])            return jsonify({'error': error_msg}), 500

def enhance_prompt():            

    """Enhance user prompts using AI."""    except Exception as e:

    try:        logger.error(f"Chat endpoint error: {str(e)}")

        data = request.get_json()        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

        prompt = data.get('prompt', '').strip()

        enhancement_level = data.get('enhancement_level', 'moderate')@app.route('/api/enhance-prompt', methods=['POST'])

        def enhance_prompt():

        if not prompt:    """Enhance user prompts using AI."""

            return jsonify({    global agent_factory

                'error': 'Prompt is required',    

                'success': False    if not agent_factory:

            }), 400        if not initialize_agent_factory():

                    return jsonify({

        # Use OpenAI agent for prompt enhancement by default                'error': 'Agent factory not initialized',

        enhancer = create_agent('openai', 'gpt-4')                'success': False

        enhanced_data = enhancer.enhance_prompt(prompt, enhancement_level)            }), 500

                    

        return jsonify({    try:

            'success': True,        data = request.get_json()

            'original_prompt': prompt,        prompt = data.get('prompt', '').strip()

            'enhanced_prompt': enhanced_data.get('enhanced_prompt', prompt),        enhancement_level = data.get('enhancement_level', 'moderate')

            'improvements': enhanced_data.get('improvements', [])        

        })        if not prompt:

                    return jsonify({

    except Exception as e:                'error': 'Prompt is required',

        logger.error(f"Prompt enhancement error: {str(e)}")                'success': False

        return jsonify({            }), 400

            'error': f'Enhancement failed: {str(e)}',        

            'success': False        # Use OpenAI agent for prompt enhancement by default

        }), 500        enhancer = agent_factory.create_agent(ProviderType.OPENAI, 'gpt-4')

        if not enhancer:

@app.route('/api/set-custom-ruleset', methods=['POST'])            return jsonify({

def set_custom_ruleset():                'error': 'Failed to create prompt enhancement agent',

    """Set a custom ruleset for the session."""                'success': False

    global custom_rulesets            }), 500

            

    try:        enhanced_data = enhancer.enhance_prompt(prompt, enhancement_level)

        data = request.get_json()        

        ruleset = data.get('ruleset', '').strip()        return jsonify({

        session_id = request.remote_addr            'success': True,

                    'original_prompt': prompt,

        if not ruleset:            'enhanced_prompt': enhanced_data.get('enhanced_prompt', prompt),

            return jsonify({'error': 'Ruleset is required'}), 400            'improvements': enhanced_data.get('improvements', [])

                })

        custom_rulesets[session_id] = ruleset        

        logger.info(f"Custom ruleset set for session {session_id}")    except Exception as e:

                logger.error(f"Prompt enhancement error: {str(e)}")

        return jsonify({'success': True, 'message': 'Custom ruleset applied'})        return jsonify({

                    'error': f'Enhancement failed: {str(e)}',

    except Exception as e:            'success': False

        logger.error(f"Set custom ruleset error: {str(e)}")        }), 500

        return jsonify({'error': f'Failed to set ruleset: {str(e)}'}), 500

@app.route('/api/set-custom-ruleset', methods=['POST'])

@app.route('/api/clear-custom-ruleset', methods=['POST'])def set_custom_ruleset():

def clear_custom_ruleset():    """Set a custom ruleset for the session."""

    """Clear the custom ruleset for the session."""    global custom_rulesets

    global custom_rulesets    

        try:

    try:        data = request.get_json()

        session_id = request.remote_addr        ruleset = data.get('ruleset', '').strip()

        custom_rulesets.pop(session_id, None)        session_id = request.remote_addr

        logger.info(f"Custom ruleset cleared for session {session_id}")        

                if not ruleset:

        return jsonify({'success': True, 'message': 'Custom ruleset cleared'})            return jsonify({'error': 'Ruleset is required'}), 400

                

    except Exception as e:        custom_rulesets[session_id] = ruleset

        logger.error(f"Clear custom ruleset error: {str(e)}")        logger.info(f"Custom ruleset set for session {session_id}")

        return jsonify({'error': f'Failed to clear ruleset: {str(e)}'}), 500        

        return jsonify({'success': True, 'message': 'Custom ruleset applied'})

@app.route('/api/clear', methods=['POST'])        

def clear_history():    except Exception as e:

    """Clear the chat history."""        logger.error(f"Set custom ruleset error: {str(e)}")

    global chat_history        return jsonify({'error': f'Failed to set ruleset: {str(e)}'}), 500

    

    try:@app.route('/api/clear-custom-ruleset', methods=['POST'])

        chat_history = []def clear_custom_ruleset():

        return jsonify({'success': True, 'message': 'Chat history cleared'})    """Clear the custom ruleset for the session."""

    except Exception as e:    global custom_rulesets

        logger.error(f"Clear history error: {str(e)}")    

        return jsonify({'error': f'Failed to clear history: {str(e)}'}), 500    try:

        session_id = request.remote_addr

@app.route('/api/images')        custom_rulesets.pop(session_id, None)

def get_images():        logger.info(f"Custom ruleset cleared for session {session_id}")

    """Get list of generated images."""        

    try:        return jsonify({'success': True, 'message': 'Custom ruleset cleared'})

        # Get images from generated_images directory        

        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generated_images')    except Exception as e:

        if not os.path.exists(images_dir):        logger.error(f"Clear custom ruleset error: {str(e)}")

            os.makedirs(images_dir)        return jsonify({'error': f'Failed to clear ruleset: {str(e)}'}), 500

            

        images = []@app.route('/api/clear', methods=['POST'])

        for filename in os.listdir(images_dir):def clear_history():

            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):    """Clear the chat history."""

                file_path = os.path.join(images_dir, filename)    global chat_history

                file_stat = os.stat(file_path)    

                images.append({    try:

                    'url': f'/generated_images/{filename}',        chat_history = []

                    'filename': filename,        return jsonify({'success': True, 'message': 'Chat history cleared'})

                    'size': file_stat.st_size,    except Exception as e:

                    'created': datetime.fromtimestamp(file_stat.st_mtime).isoformat()        logger.error(f"Clear history error: {str(e)}")

                })        return jsonify({'error': f'Failed to clear history: {str(e)}'}), 500

        

        # Sort by creation time, newest <EMAIL>('/api/images')

        images.sort(key=lambda x: x['created'], reverse=True)def get_images():

            """Get list of generated images."""

        return jsonify({'images': images})    try:

                # Get images from generated_images directory

    except Exception as e:        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generated_images')

        logger.error(f"Get images error: {str(e)}")        if not os.path.exists(images_dir):

        return jsonify({'images': []})            os.makedirs(images_dir)

            

@app.route('/generated_images/<filename>')        images = []

def serve_image(filename):        for filename in os.listdir(images_dir):

    """Serve generated images."""            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):

    try:                file_path = os.path.join(images_dir, filename)

        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generated_images')                file_stat = os.stat(file_path)

        return send_from_directory(images_dir, filename)                images.append({

    except Exception as e:                    'url': f'/generated_images/{filename}',

        logger.error(f"Serve image error: {str(e)}")                    'filename': filename,

        return "Image not found", 404                    'size': file_stat.st_size,

                    'created': datetime.fromtimestamp(file_stat.st_mtime).isoformat()

@app.route('/health')                })

def health():        

    """Health check endpoint."""        # Sort by creation time, newest first

    return jsonify({        images.sort(key=lambda x: x['created'], reverse=True)

        'status': 'healthy',        

        'providers': ['openai', 'recraft', 'imagen', 'flux'],        return jsonify({'images': images})

        'timestamp': datetime.now().isoformat()        

    })    except Exception as e:

        logger.error(f"Get images error: {str(e)}")

if __name__ == '__main__':        return jsonify({'images': []})

    print("Starting Multi-Provider Image Generation Web Interface")

    print("Available providers: OpenAI, Recraft, Google Imagen, FLUX")@app.route('/generated_images/<filename>')

    print("Open http://localhost:5000 in your browser")def serve_image(filename):

    print("Note: This is currently using mock agents for testing the frontend")    """Serve generated images."""

        try:

    # Create generated_images directory if it doesn't exist        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generated_images')

    images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generated_images')        return send_from_directory(images_dir, filename)

    if not os.path.exists(images_dir):    except Exception as e:

        os.makedirs(images_dir)        logger.error(f"Serve image error: {str(e)}")

            return "Image not found", 404

    app.run(debug=True, host='0.0.0.0', port=5000)
@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'providers': ['openai', 'recraft', 'imagen', 'flux'],
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("Starting Multi-Provider Image Generation Web Interface")
    print("Available providers: OpenAI, Recraft, Google Imagen, FLUX")
    print("Open http://localhost:5000 in your browser")
    
    # Create generated_images directory if it doesn't exist
    images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generated_images')
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)
    
    # Initialize agent factory on startup
    initialize_agent_factory()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
    global prompt_enhancer
    try:
        if Settings.PROMPT_ENHANCEMENT_ENABLED:
            prompt_enhancer = PromptEnhancementTool()
            logger.info("Prompt enhancer initialized successfully")
            return True
        else:
            logger.info("Prompt enhancement is disabled in settings")
            return True
    except Exception as e:
        logger.error(f"Failed to initialize prompt enhancer: {str(e)}")
        return False

@app.route('/')
def index():
    """Serve the main chat interface."""
    return render_template('chat.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages."""
    global agent, chat_history, custom_rulesets

    if not agent:
        if not initialize_agent():
            return jsonify({
                'error': 'Agent initialization failed. Please check your API key and try again.'
            }), 500

    try:
        # Check for custom ruleset and apply it
        session_id = request.remote_addr
        if session_id in custom_rulesets:
            ruleset = custom_rulesets[session_id]
            # Apply ruleset to current request context
            if agent:
                agent.ensure_custom_ruleset(ruleset)
        data = request.get_json()
        user_message = data.get('message', '').strip()
        parameters = data.get('parameters', {})
        uploaded_images = data.get('images', [])
        
        if not user_message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Handle API mode selection
        api_mode = parameters.get('api_mode', 'responses')  # Default to responses API
        enhanced_message = user_message

        # Add API mode instruction to the message
        if api_mode == 'legacy':
            enhanced_message += " [Use OpenAIImageGenerator or OpenAIImageEditor tools only - no automatic prompt enhancement]"
        else:
            enhanced_message += " [Use ResponsesImageTool for advanced features]"

        # Add other parameters to the message if any are specified
        if parameters:
            # Remove api_mode from parameters since we handled it separately
            other_params = {k: v for k, v in parameters.items() if k != 'api_mode'}
            if other_params:
                param_string = ", ".join([f"{k}={v}" for k, v in other_params.items()])
                enhanced_message += f" [Additional parameters: {param_string}]"

        # Process uploaded images using ImageEncoder
        processed_images = None
        if uploaded_images:
            try:
                processed_images = ImageEncoder.process_uploaded_images(uploaded_images)
                success_count = processed_images['success_count']
                error_count = len(processed_images['errors'])

                if success_count > 0:
                    image_info = f" [Reference images: {success_count} image(s) processed and ready for use]"
                    enhanced_message += image_info
                    logger.info(f"Successfully processed {success_count}/{len(uploaded_images)} uploaded images")

                    if error_count > 0:
                        logger.warning(f"Failed to process {error_count} images: {processed_images['errors']}")
                else:
                    logger.error(f"Failed to process any uploaded images: {processed_images['errors']}")
                    return jsonify({'error': f'Failed to process uploaded images: {"; ".join(processed_images["errors"])}'}), 400

            except Exception as e:
                logger.error(f"Error processing uploaded images: {str(e)}")
                return jsonify({'error': f'Error processing uploaded images: {str(e)}'}), 400
        
        # Process with agent, passing processed images if available
        if processed_images:
            response = agent.invoke(enhanced_message, processed_images=processed_images)
        else:
            response = agent.invoke(enhanced_message)
        
        # Extract images if any were generated
        images = []
        if hasattr(response, 'get') and 'output' in response:
            # Look for base64 image data in response
            output_text = response['output']
            # Parse any base64 image data and save
            # This would need to be implemented based on your agent's response format
        
        # Prepare response data
        response_data = {
            'response': response['output'] if isinstance(response, dict) else str(response),
            'images': images,
            'uploaded_images_count': len(uploaded_images)
        }

        # Add processed image information
        if processed_images:
            response_data.update({
                'processed_images_count': processed_images['success_count'],
                'image_processing_errors': processed_images['errors']
            })

            # Clean up temporary files
            try:
                for temp_file in processed_images['temp_files']:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        logger.debug(f"Cleaned up temporary file: {temp_file}")
            except Exception as e:
                logger.warning(f"Error cleaning up temporary files: {str(e)}")

        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear', methods=['POST'])
def clear_history():
    """Clear chat history."""
    global chat_history
    chat_history = []
    return jsonify({'message': 'Chat history cleared'})

@app.route('/api/enhance-prompt', methods=['POST'])
def enhance_prompt():
    """Enhance a user prompt for better image generation."""
    global prompt_enhancer

    # Check if prompt enhancement is enabled
    if not Settings.PROMPT_ENHANCEMENT_ENABLED:
        return jsonify({
            'error': 'Prompt enhancement is disabled'
        }), 400

    # Initialize prompt enhancer if not already done
    if not prompt_enhancer:
        if not initialize_prompt_enhancer():
            return jsonify({
                'error': 'Prompt enhancement service is not available'
            }), 500

    try:
        data = request.get_json()
        original_prompt = data.get('prompt', '').strip()
        context = data.get('context', '')
        style_preference = data.get('style_preference', '')
        enhancement_level = data.get('enhancement_level', 'moderate')

        if not original_prompt:
            return jsonify({'error': 'Prompt is required'}), 400

        # Validate enhancement level
        valid_levels = ['light', 'moderate', 'extensive']
        if enhancement_level not in valid_levels:
            enhancement_level = 'moderate'

        logger.info(f"Enhancing prompt: '{original_prompt[:50]}...'")

        # Use the prompt enhancement tool
        result_json = prompt_enhancer._run(
            original_prompt=original_prompt,
            context=context if context else None,
            style_preference=style_preference if style_preference else None,
            enhancement_level=enhancement_level
        )

        # Parse the result
        import json
        result = json.loads(result_json)

        logger.info("Prompt enhancement completed successfully")
        return jsonify(result)

    except Exception as e:
        logger.error(f"Prompt enhancement error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'original_prompt': data.get('prompt', '') if 'data' in locals() else ''
        }), 500

@app.route('/api/set-custom-ruleset', methods=['POST'])
def set_custom_ruleset():
    """Set a custom ruleset for the agent."""
    global agent, custom_rulesets

    try:
        data = request.get_json()
        ruleset = data.get('ruleset', '').strip()

        if not ruleset:
            return jsonify({'error': 'Ruleset is required'}), 400

        # Validate ruleset length
        if len(ruleset) > 2000:
            return jsonify({'error': 'Ruleset exceeds maximum length of 2000 characters'}), 400

        # Basic prompt injection prevention
        forbidden_patterns = ['ignore previous', 'disregard above', 'forget all', 'system:', 'assistant:']
        ruleset_lower = ruleset.lower()
        for pattern in forbidden_patterns:
            if pattern in ruleset_lower:
                return jsonify({'error': f'Ruleset contains forbidden pattern: {pattern}'}), 400

        # Store ruleset for the session (using a simple session ID based on request)
        session_id = request.remote_addr  # Simple session tracking
        custom_rulesets[session_id] = ruleset

        # Re-initialize agent with custom ruleset if it exists
        if agent:
            agent.set_custom_ruleset(ruleset)
            logger.info(f"Custom ruleset applied for session {session_id}")

        return jsonify({
            'success': True,
            'message': 'Custom ruleset applied successfully',
            'ruleset_length': len(ruleset)
        })

    except Exception as e:
        logger.error(f"Error setting custom ruleset: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear-custom-ruleset', methods=['POST'])
def clear_custom_ruleset():
    """Clear the custom ruleset for the agent."""
    global agent, custom_rulesets

    try:
        session_id = request.remote_addr

        # Remove custom ruleset
        if session_id in custom_rulesets:
            del custom_rulesets[session_id]

        # Reset agent to default behavior
        if agent:
            agent.clear_custom_ruleset()
            logger.info(f"Custom ruleset cleared for session {session_id}")

        return jsonify({
            'success': True,
            'message': 'Custom ruleset cleared successfully'
        })

    except Exception as e:
        logger.error(f"Error clearing custom ruleset: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/images/<filename>')
def serve_image(filename):
    """Serve generated images."""
    return send_from_directory('generated_images', filename)

@app.route('/api/images')
def list_images():
    """List all available images."""
    try:
        images = []
        if os.path.exists('generated_images'):
            for filename in os.listdir('generated_images'):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
                    file_path = os.path.join('generated_images', filename)
                    file_stats = os.stat(file_path)
                    images.append({
                        'filename': filename,
                        'url': f'/images/{filename}',
                        'size': file_stats.st_size,
                        'created': datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    })
        
        # Sort by creation time, newest first
        images.sort(key=lambda x: x['created'], reverse=True)
        
        return jsonify({'images': images})
    except Exception as e:
        logger.error(f"Error listing images: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health')
def health():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'agent_initialized': agent is not None,
        'timestamp': datetime.now().isoformat()
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('generated_images', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Initialize agent on startup
    print("🚀 Starting Langchain Image Generation Agent Web Interface...")
    print("⚙️  Initializing agent...")

    agent_ok = initialize_agent()
    enhancer_ok = initialize_prompt_enhancer()

    if agent_ok:
        print("✅ Agent initialized successfully!")
        if enhancer_ok and Settings.PROMPT_ENHANCEMENT_ENABLED:
            print("✅ Prompt enhancer initialized successfully!")
        elif Settings.PROMPT_ENHANCEMENT_ENABLED:
            print("⚠️  Prompt enhancer failed to initialize")
        else:
            print("ℹ️  Prompt enhancement disabled")
        print("🌐 Starting web server...")
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        print("❌ Failed to initialize agent. Please check your configuration.")
