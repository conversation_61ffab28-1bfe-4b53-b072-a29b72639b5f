"""
Enhanced image generation tool with automatic saving capability.
"""
from typing import Optional, Type
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
import openai
import base64
import os
from datetime import datetime
from config.settings import Settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutoSaveImageInput(BaseModel):
    """Input schema for the auto-save image generation tool."""
    prompt: str = Field(description="Detailed text prompt describing the image to generate")
    save_to_file: Optional[bool] = Field(
        default=True,
        description="Whether to automatically save the image to a file (default: True)"
    )
    filename: Optional[str] = Field(
        default=None,
        description="Custom filename for the saved image (auto-generated if not provided)"
    )
    size: Optional[str] = Field(
        default=Settings.DEFAULT_IMAGE_SIZE,
        description="Size of the image ('auto', '1024x1024', '1536x1024', '1024x1536')"
    )
    quality: Optional[str] = Field(
        default=Settings.DEFAULT_IMAGE_QUALITY,
        description="Quality of the image ('auto', 'high', 'medium', 'low')"
    )
    background: Optional[str] = Field(
        default=Settings.DEFAULT_BACKGROUND,
        description="Background setting ('auto', 'transparent', 'opaque')"
    )

class AutoSaveImageGenerator(BaseTool):
    """Enhanced image generation tool that automatically saves images to files."""
    
    name: str = "AutoSaveImageGenerator"
    description: str = (
        "Generate images with gpt-image-1 and automatically save them to files. "
        "Perfect for when users want to immediately use or view their generated images. "
        "Returns both the file path and image information."
    )
    args_schema: Type[BaseModel] = AutoSaveImageInput
    
    def __init__(self):
        """Initialize the auto-save image generation tool."""
        super().__init__()
        logger.info("Initialized Auto-Save Image Generator with gpt-image-1")
    
    def _get_client(self):
        """Get the OpenAI client instance."""
        return openai.OpenAI(api_key=Settings.OPENAI_API_KEY)
    
    def _save_image(self, base64_data: str, filename: str = None) -> str:
        """Save base64 image data to file."""
        try:
            # Create directory
            os.makedirs("generated_images", exist_ok=True)
            
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_image_{timestamp}.png"
            
            # Ensure proper extension
            if not filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
                filename += '.png'
            
            filepath = os.path.join("generated_images", filename)
            
            # Decode and save
            image_bytes = base64.b64decode(base64_data)
            
            with open(filepath, 'wb') as f:
                f.write(image_bytes)
            
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving image: {str(e)}")
            return None
    
    def _run(
        self,
        prompt: str,
        save_to_file: bool = True,
        filename: str = None,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        background: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Execute the auto-save image generation tool.
        
        Args:
            prompt: Text prompt describing the image
            save_to_file: Whether to save to file automatically
            filename: Custom filename (optional)
            size: Image size (optional)
            quality: Image quality (optional)
            background: Background setting (optional)
            run_manager: Callback manager (optional)
            
        Returns:
            str: Success message with file path and image details
        """
        try:
            # Use defaults
            size = size or Settings.DEFAULT_IMAGE_SIZE
            quality = quality or Settings.DEFAULT_IMAGE_QUALITY
            background = background or Settings.DEFAULT_BACKGROUND
            
            logger.info(f"Generating and auto-saving image: '{prompt[:50]}...'")
            
            # Generate image
            client = self._get_client()
            response = client.images.generate(
                model=Settings.IMAGE_MODEL,
                prompt=prompt,
                size=size,
                quality=quality,
                background=background,
                output_format="png",
                n=1
            )
            
            # Get image data
            image_b64 = response.data[0].b64_json
            
            result_parts = []
            
            # Save to file if requested
            if save_to_file:
                filepath = self._save_image(image_b64, filename)
                if filepath:
                    abs_path = os.path.abspath(filepath)
                    result_parts.append(f"✅ Image saved to: {abs_path}")
                    result_parts.append(f"📁 File size: {len(base64.b64decode(image_b64)):,} bytes")
                else:
                    result_parts.append("⚠️ Image generated but failed to save to file")
            
            # Add generation details
            result_parts.append(f"🎨 Generated with gpt-image-1")
            result_parts.append(f"📐 Size: {size}")
            result_parts.append(f"⭐ Quality: {quality}")
            result_parts.append(f"🎭 Background: {background}")
            
            # Token usage if available
            if hasattr(response, 'usage') and response.usage:
                usage = response.usage
                result_parts.append(f"🔢 Tokens used: {usage.total_tokens}")
            
            if not save_to_file:
                result_parts.append(f"📋 Base64 length: {len(image_b64)} characters")
                result_parts.append("💡 Use the image decoder utility to save this image later")
            
            return "\n".join(result_parts)
            
        except openai.OpenAIError as e:
            error_msg = f"OpenAI API error: {str(e)}"
            logger.error(error_msg)
            return f"❌ Error generating image: {error_msg}"
        
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return f"❌ Error: {error_msg}"
    
    async def _arun(self, *args, **kwargs) -> str:
        """Async version - uses sync implementation."""
        return self._run(*args, **kwargs)

# Test function
def test_auto_save_tool():
    """Test the auto-save image generation tool."""
    try:
        tool = AutoSaveImageGenerator()
        result = tool._run("A simple test image of a red circle", save_to_file=True)
        
        if "Error" not in result and "✅" in result:
            print("✓ Auto-save image tool test passed")
            return True
        else:
            print(f"✗ Auto-save image tool test failed: {result}")
            return False
    except Exception as e:
        print(f"✗ Auto-save tool test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_auto_save_tool()