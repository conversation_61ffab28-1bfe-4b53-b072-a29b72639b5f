"""
Configuration settings for the Recraft Image Generation Agent.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    """Configuration settings for the Recraft Image Generation Agent."""

    # Recraft API Configuration
    RECRAFT_API_KEY: str = os.getenv("RECRAFT_API_KEY", "")
    RECRAFT_API_URL: str = os.getenv("RECRAFT_API_URL", "https://external.api.recraft.ai/v1")
    RECRAFT_CLIENT_TYPE: str = os.getenv("RECRAFT_CLIENT_TYPE", "Image_Agent_Recraft")

    # Model Configuration
    LANGUAGE_MODEL: str = os.getenv("LANGUAGE_MODEL", "gpt-4o-mini")
    RECRAFT_MODEL: str = os.getenv("RECRAFT_MODEL", "recraftv3")

    # Default Image Generation Parameters
    DEFAULT_IMAGE_SIZE: str = os.getenv("DEFAULT_IMAGE_SIZE", "1024x1024")
    DEFAULT_IMAGE_QUALITY: str = os.getenv("DEFAULT_IMAGE_QUALITY", "high")
    DEFAULT_OUTPUT_FORMAT: str = os.getenv("DEFAULT_OUTPUT_FORMAT", "png")
    DEFAULT_STYLE: str = os.getenv("DEFAULT_STYLE", "any")
    DEFAULT_SUBSTYLE: str = os.getenv("DEFAULT_SUBSTYLE", "")

    # Vector Graphics Configuration
    VECTOR_GENERATION_ENABLED: bool = os.getenv("VECTOR_GENERATION_ENABLED", "true").lower() == "true"
    VECTOR_OUTPUT_FORMATS: list = os.getenv("VECTOR_OUTPUT_FORMATS", "svg,pdf").split(",")
    DEFAULT_VECTOR_FORMAT: str = os.getenv("DEFAULT_VECTOR_FORMAT", "svg")

    # Application Configuration
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # File Storage Configuration
    GENERATED_IMAGES_DIR: str = os.getenv("GENERATED_IMAGES_DIR", "generated_images")

    # Image Processing Configuration
    MAX_IMAGE_SIZE: str = os.getenv("MAX_IMAGE_SIZE", "50MB")
    SUPPORTED_FORMATS: list = os.getenv("SUPPORTED_FORMATS", "png,jpg,jpeg,svg,pdf,webp").split(",")
    IMAGE_COMPRESSION: int = int(os.getenv("IMAGE_COMPRESSION", "85"))

    # Web Interface Configuration
    FLASK_HOST: str = os.getenv("FLASK_HOST", "0.0.0.0")
    FLASK_PORT: int = int(os.getenv("FLASK_PORT", "5000"))
    FLASK_SECRET_KEY: str = os.getenv("FLASK_SECRET_KEY", "recraft-image-agent-secret-key")

    # Vector Graphics Processing
    CAIRO_BACKEND: bool = os.getenv("CAIRO_BACKEND", "true").lower() == "true"
    SVG_OPTIMIZATION: bool = os.getenv("SVG_OPTIMIZATION", "true").lower() == "true"
    PDF_QUALITY: int = int(os.getenv("PDF_QUALITY", "300"))

    # Style and Substyle Options (based on Recraft API)
    AVAILABLE_STYLES = [
        "",
        "any",
        "realistic_image",
        "digital_illustration"
    ]

    AVAILABLE_SUBSTYLES = [
        "",
        "b_and_w",
        "hard_flash", 
        "hdr",
        "natural_light",
        "studio_portrait",
        "enterprise",
        "motion_blur",
        "pixel_art",
        "hand_drawn",
        "grain",
        "infantile_sketch",
        "2d_art_poster",
        "handmade_3d",
        "hand_drawn_outline",
        "engraving_color",
        "2d_art_poster_2",
    ]

    AVAILABLE_IMAGE_SIZES = [
        "",
        "1024x1024",
        "1024x1280", 
        "1024x1365",
        "1024x1434",
        "1024x1536",
        "1024x1707",
        "1024x1820",
        "1024x2048",
        "1280x1024",
        "1365x1024",
        "1434x1024",
        "1536x1024",
        "1707x1024",
        "1820x1024",
        "2048x1024",
    ]

    @classmethod
    def validate_settings(cls) -> bool:
        """Validate that required settings are present."""
        if not cls.RECRAFT_API_KEY:
            raise ValueError("RECRAFT_API_KEY environment variable is required")
        
        # Validate API URL
        if not cls.RECRAFT_API_URL or not cls.RECRAFT_API_URL.startswith("http"):
            raise ValueError("RECRAFT_API_URL must be a valid HTTP(S) URL")
        
        # Validate image size if provided
        if cls.DEFAULT_IMAGE_SIZE and cls.DEFAULT_IMAGE_SIZE not in cls.AVAILABLE_IMAGE_SIZES:
            available = ", ".join([size for size in cls.AVAILABLE_IMAGE_SIZES if size])
            raise ValueError(f"DEFAULT_IMAGE_SIZE must be one of: {available}")
        
        # Validate style if provided
        if cls.DEFAULT_STYLE and cls.DEFAULT_STYLE not in cls.AVAILABLE_STYLES:
            available = ", ".join([style for style in cls.AVAILABLE_STYLES if style])
            raise ValueError(f"DEFAULT_STYLE must be one of: {available}")
        
        # Validate substyle if provided
        if cls.DEFAULT_SUBSTYLE and cls.DEFAULT_SUBSTYLE not in cls.AVAILABLE_SUBSTYLES:
            available = ", ".join([substyle for substyle in cls.AVAILABLE_SUBSTYLES if substyle])
            raise ValueError(f"DEFAULT_SUBSTYLE must be one of: {available}")
        
        return True

    @classmethod
    def validate(cls) -> bool:
        """Validate all settings (alias for validate_settings for compatibility)."""
        return cls.validate_settings()

    @classmethod
    def get_recraft_config(cls) -> dict:
        """Get Recraft-specific configuration as a dictionary."""
        return {
            "api_key": cls.RECRAFT_API_KEY,
            "api_url": cls.RECRAFT_API_URL,
            "client_type": cls.RECRAFT_CLIENT_TYPE,
            "model": cls.RECRAFT_MODEL,
            "default_size": cls.DEFAULT_IMAGE_SIZE,
            "default_style": cls.DEFAULT_STYLE,
            "default_substyle": cls.DEFAULT_SUBSTYLE,
            "vector_enabled": cls.VECTOR_GENERATION_ENABLED,
        }

    @classmethod
    def get_vector_config(cls) -> dict:
        """Get vector graphics configuration."""
        return {
            "enabled": cls.VECTOR_GENERATION_ENABLED,
            "formats": cls.VECTOR_OUTPUT_FORMATS,
            "default_format": cls.DEFAULT_VECTOR_FORMAT,
            "cairo_backend": cls.CAIRO_BACKEND,
            "svg_optimization": cls.SVG_OPTIMIZATION,
            "pdf_quality": cls.PDF_QUALITY,
        }