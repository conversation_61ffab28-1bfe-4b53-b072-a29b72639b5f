#!/usr/bin/env python3
"""
Simple test to verify the prompt enhancement control changes are syntactically correct.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_responses_tool_import():
    """Test that ResponsesImageTool can be imported and has the new parameter."""
    print("🧪 Testing ResponsesImageTool import and parameter...")
    
    try:
        # Test import
        from src.responses_image_tool import ResponsesImageInput
        print("✅ ResponsesImageTool imported successfully")
        
        # Check if the new parameter is in the input schema
        input_schema = ResponsesImageInput()
        if hasattr(input_schema, 'show_revised_prompt'):
            print("✅ show_revised_prompt parameter found in ResponsesImageInput")
        else:
            print("❌ show_revised_prompt parameter missing from ResponsesImageInput")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ResponsesImageTool import test failed: {str(e)}")
        return False

def test_agent_import():
    """Test that ImageGenerationAgent can be imported."""
    print("\n🧪 Testing ImageGenerationAgent import...")
    
    try:
        # Test import without full initialization
        import src.agent
        print("✅ Agent module imported successfully")
        
        # Check if the agent class exists
        if hasattr(src.agent, 'ImageGenerationAgent'):
            print("✅ ImageGenerationAgent class found")
        else:
            print("❌ ImageGenerationAgent class missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent import test failed: {str(e)}")
        return False

def test_system_prompt_content():
    """Test that the system prompt contains the new guidelines."""
    print("\n🧪 Testing system prompt content...")
    
    try:
        # Read the agent file and check for our changes
        with open('src/agent.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the new guideline
        if "show_revised_prompt parameter" in content:
            print("✅ show_revised_prompt guideline found in agent.py")
        else:
            print("❌ show_revised_prompt guideline missing from agent.py")
            return False
        
        # Check for the new example
        if "show_revised_prompt=True" in content:
            print("✅ show_revised_prompt example found in agent.py")
        else:
            print("❌ show_revised_prompt example missing from agent.py")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ System prompt content test failed: {str(e)}")
        return False

def test_html_ui_changes():
    """Test that the HTML UI includes the new control."""
    print("\n🧪 Testing HTML UI changes...")
    
    try:
        # Read the HTML file and check for our changes
        with open('templates/chat.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the new control
        if "Show AI-Optimized Prompt" in content:
            print("✅ 'Show AI-Optimized Prompt' control found in chat.html")
        else:
            print("❌ 'Show AI-Optimized Prompt' control missing from chat.html")
            return False
        
        # Check for the new parameter in JavaScript
        if "showRevisedPrompt" in content:
            print("✅ showRevisedPrompt parameter found in JavaScript")
        else:
            print("❌ showRevisedPrompt parameter missing from JavaScript")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ HTML UI changes test failed: {str(e)}")
        return False

def main():
    """Run all simple tests."""
    print("🚀 Starting Simple Prompt Enhancement Control Tests...")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: ResponsesImageTool import and parameter
    if not test_responses_tool_import():
        all_tests_passed = False
    
    # Test 2: Agent import
    if not test_agent_import():
        all_tests_passed = False
    
    # Test 3: System prompt content
    if not test_system_prompt_content():
        all_tests_passed = False
    
    # Test 4: HTML UI changes
    if not test_html_ui_changes():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 ALL SIMPLE TESTS PASSED! Code changes are syntactically correct.")
        print("\n📋 Summary of verified changes:")
        print("✅ ResponsesImageTool has show_revised_prompt parameter")
        print("✅ Agent system prompt includes new guidelines")
        print("✅ HTML UI includes new control")
        print("✅ JavaScript handles new parameter")
        return True
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
