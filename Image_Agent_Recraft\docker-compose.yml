version: '3.8'

services:
  recraft-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: recraft-image-agent
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
    env_file:
      - .env
    volumes:
      - ./generated_images:/app/generated_images
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - recraft-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: recraft-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - recraft-network
    command: redis-server --appendonly yes

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: recraft-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./generated_images:/var/www/generated_images:ro
    depends_on:
      - recraft-agent
    restart: unless-stopped
    networks:
      - recraft-network
    profiles:
      - production

volumes:
  redis_data:
    driver: local

networks:
  recraft-network:
    driver: bridge