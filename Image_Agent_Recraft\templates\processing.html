{% extends "base.html" %}

{% block title %}Image Processing - Recraft Vector Graphics Agent{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-image me-2"></i>Image Processing & Enhancement
                </h2>
                <p class="mb-0 text-muted">Upload and transform your images with AI-powered processing</p>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <!-- File Upload -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Upload Image</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="image-upload" accept="image/*" onchange="previewImage()">
                                    <div class="form-text">Supported: PNG, JPG, GIF, SVG (max 16MB)</div>
                                </div>
                                
                                <!-- Image Preview -->
                                <div id="image-preview" style="display: none;">
                                    <img id="preview-img" class="img-fluid rounded" style="max-height: 200px;">
                                    <div class="mt-2">
                                        <small class="text-muted" id="image-info"></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Processing Options -->
                        <form id="processing-form">
                            <div class="mb-3">
                                <label for="operation" class="form-label">Processing Operation:</label>
                                <select class="form-select" id="operation" onchange="updateInstructions()">
                                    <option value="enhance">Enhance Quality</option>
                                    <option value="remove_background">Remove Background</option>
                                    <option value="upscale">Upscale (2x)</option>
                                    <option value="style_transfer">Style Transfer</option>
                                    <option value="color_correction">Color Correction</option>
                                    <option value="vectorize">Convert to Vector</option>
                                    <option value="format_convert">Format Conversion</option>
                                    <option value="custom">Custom Processing</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="instructions" class="form-label">Additional Instructions:</label>
                                <textarea 
                                    class="form-control" 
                                    id="instructions" 
                                    rows="3" 
                                    placeholder="Describe any specific requirements or modifications..."
                                ></textarea>
                            </div>
                            
                            <div class="row" id="advanced-options" style="display: none;">
                                <div class="col-md-6 mb-3">
                                    <label for="output-format" class="form-label">Output Format:</label>
                                    <select class="form-select" id="output-format">
                                        <option value="png">PNG</option>
                                        <option value="jpg">JPG</option>
                                        <option value="svg">SVG</option>
                                        <option value="pdf">PDF</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="quality" class="form-label">Quality Level:</label>
                                    <select class="form-select" id="quality">
                                        <option value="standard">Standard</option>
                                        <option value="high">High</option>
                                        <option value="maximum">Maximum</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="show-advanced" onchange="toggleAdvanced()">
                                <label class="form-check-label" for="show-advanced">
                                    Show advanced options
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-recraft w-100" id="process-btn" disabled>
                                <i class="fas fa-cogs me-2"></i>Process Image
                            </button>
                        </form>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card bg-light h-100">
                            <div class="card-body">
                                <h5><i class="fas fa-magic me-2"></i>Processing Capabilities</h5>
                                
                                <div class="accordion" id="capabilitiesAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#enhance">
                                                <i class="fas fa-sparkles me-2"></i>Enhancement
                                            </button>
                                        </h2>
                                        <div id="enhance" class="accordion-collapse collapse" data-bs-parent="#capabilitiesAccordion">
                                            <div class="accordion-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><i class="fas fa-check text-success me-2"></i>Sharpen details</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Reduce noise</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Improve contrast</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Color enhancement</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#background">
                                                <i class="fas fa-cut me-2"></i>Background Removal
                                            </button>
                                        </h2>
                                        <div id="background" class="accordion-collapse collapse" data-bs-parent="#capabilitiesAccordion">
                                            <div class="accordion-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><i class="fas fa-check text-success me-2"></i>Automatic detection</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Clean edges</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Transparent output</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Professional quality</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#vector">
                                                <i class="fas fa-vector-square me-2"></i>Vectorization
                                            </button>
                                        </h2>
                                        <div id="vector" class="accordion-collapse collapse" data-bs-parent="#capabilitiesAccordion">
                                            <div class="accordion-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><i class="fas fa-check text-success me-2"></i>Convert to SVG</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Scalable output</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Optimized paths</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>Multiple formats</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Animation -->
<div class="loading-spinner" id="processing-loading">
    <div class="text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Processing image...</span>
        </div>
        <h5>Processing Your Image</h5>
        <p class="text-muted">Please wait while we enhance your image...</p>
    </div>
</div>

<!-- Results Section -->
<div id="processing-results" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="mb-0">
                <i class="fas fa-check-circle me-2"></i>Processing Complete
            </h3>
            <div>
                <button class="btn btn-outline-secondary btn-sm me-2" onclick="downloadResult()">
                    <i class="fas fa-download me-1"></i>Download
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="processAnother()">
                    <i class="fas fa-redo me-1"></i>Process Another
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Original</h5>
                    <div id="original-image" class="text-center">
                        <!-- Original image preview -->
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Processed</h5>
                    <div id="processed-result" class="text-center">
                        <!-- Processed result will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <div id="processing-details" class="p-3 bg-light rounded">
                        <!-- Processing details and response -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Processing Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100 h-100" onclick="quickProcess('enhance')">
                            <i class="fas fa-sparkles fa-2x mb-2 d-block"></i>
                            <strong>Enhance</strong><br>
                            <small>Improve quality</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-success w-100 h-100" onclick="quickProcess('remove_background')">
                            <i class="fas fa-cut fa-2x mb-2 d-block"></i>
                            <strong>Remove BG</strong><br>
                            <small>Transparent background</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-warning w-100 h-100" onclick="quickProcess('upscale')">
                            <i class="fas fa-expand-arrows-alt fa-2x mb-2 d-block"></i>
                            <strong>Upscale</strong><br>
                            <small>Increase resolution</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-info w-100 h-100" onclick="quickProcess('vectorize')">
                            <i class="fas fa-vector-square fa-2x mb-2 d-block"></i>
                            <strong>Vectorize</strong><br>
                            <small>Convert to vector</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let uploadedFile = null;

function previewImage() {
    const input = document.getElementById('image-upload');
    const file = input.files[0];
    
    if (file) {
        uploadedFile = file;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('preview-img');
            preview.src = e.target.result;
            
            const info = document.getElementById('image-info');
            info.textContent = `${file.name} (${formatFileSize(file.size)})`;
            
            document.getElementById('image-preview').style.display = 'block';
            document.getElementById('process-btn').disabled = false;
        };
        reader.readAsDataURL(file);
    }
}

function updateInstructions() {
    const operation = document.getElementById('operation').value;
    const instructions = document.getElementById('instructions');
    
    const defaultInstructions = {
        'enhance': 'Improve image quality, sharpness, and colors while maintaining natural appearance.',
        'remove_background': 'Remove the background completely, leaving only the main subject with transparent background.',
        'upscale': 'Increase resolution by 2x while preserving details and avoiding artifacts.',
        'style_transfer': 'Apply artistic style transformation. Specify the desired style (e.g., oil painting, watercolor).',
        'color_correction': 'Adjust colors, brightness, contrast, and saturation for optimal appearance.',
        'vectorize': 'Convert to scalable vector format while preserving the essential shapes and details.',
        'format_convert': 'Convert to different image format while maintaining quality.',
        'custom': 'Describe your specific processing requirements...'
    };
    
    instructions.placeholder = defaultInstructions[operation] || 'Describe your processing requirements...';
}

function toggleAdvanced() {
    const checkbox = document.getElementById('show-advanced');
    const options = document.getElementById('advanced-options');
    options.style.display = checkbox.checked ? 'block' : 'none';
}

function quickProcess(operation) {
    if (!uploadedFile) {
        showToast('Please upload an image first', 'warning');
        return;
    }
    
    document.getElementById('operation').value = operation;
    updateInstructions();
    
    // Scroll to form
    document.getElementById('processing-form').scrollIntoView({ behavior: 'smooth' });
}

// Processing form handling
document.getElementById('processing-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!uploadedFile) {
        showToast('Please upload an image first', 'warning');
        return;
    }
    
    const operation = document.getElementById('operation').value;
    const instructions = document.getElementById('instructions').value;
    const outputFormat = document.getElementById('output-format').value;
    const quality = document.getElementById('quality').value;
    
    // Show loading
    document.getElementById('process-btn').disabled = true;
    showLoading('processing-loading');
    hideProcessingResults();
    
    try {
        const formData = new FormData();
        formData.append('file', uploadedFile);
        formData.append('operation', operation);
        formData.append('instructions', instructions);
        formData.append('output_format', outputFormat);
        formData.append('quality', quality);
        formData.append('session_id', sessionId);
        
        const response = await fetch('/api/process', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showProcessingResults(data);
            showToast('Image processed successfully!', 'success');
        } else {
            throw new Error(data.error || 'Processing failed');
        }
        
    } catch (error) {
        handleApiError(error, 'Image processing');
    } finally {
        document.getElementById('process-btn').disabled = false;
        hideLoading('processing-loading');
    }
});

function showProcessingResults(data) {
    // Show original image
    const original = document.getElementById('original-image');
    original.innerHTML = `<img src="${URL.createObjectURL(uploadedFile)}" class="img-fluid rounded" style="max-height: 300px;">`;
    
    // Show processing details
    document.getElementById('processing-details').innerHTML = data.response;
    
    // Show results section
    document.getElementById('processing-results').style.display = 'block';
    document.getElementById('processing-results').scrollIntoView({ behavior: 'smooth' });
}

function hideProcessingResults() {
    document.getElementById('processing-results').style.display = 'none';
}

function downloadResult() {
    showToast('Download feature will be available soon!', 'info');
}

function processAnother() {
    // Reset form
    document.getElementById('processing-form').reset();
    document.getElementById('image-upload').value = '';
    document.getElementById('image-preview').style.display = 'none';
    document.getElementById('process-btn').disabled = true;
    hideProcessingResults();
    uploadedFile = null;
    
    showToast('Ready for another image!', 'info');
    document.getElementById('image-upload').focus();
}

// Initialize
updateInstructions();
</script>
{% endblock %}