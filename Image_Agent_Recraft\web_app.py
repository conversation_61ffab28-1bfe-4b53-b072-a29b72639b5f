"""
Flask web application for the Recraft Image Generation Agent.
Provides web interface for vector graphics generation and image processing.
"""
import os
from flask import Flask, request, jsonify, render_template, send_from_directory
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import json
import uuid
from datetime import datetime
from src.agent import RecraftAgent
from config.settings import Settings
import logging
import base64
from PIL import Image
import io

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app setup
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure upload folders
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'generated_images'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# Allowed file extensions for uploads
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'pdf'}

# Global agent instance
agent = None
chat_sessions = {}


def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def init_agent():
    """Initialize the agent with error handling."""
    global agent
    try:
        agent = RecraftAgent()
        logger.info("Recraft agent initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize agent: {str(e)}")
        agent = None
        return False


def get_session_history(session_id):
    """Get or create chat history for a session."""
    if session_id not in chat_sessions:
        chat_sessions[session_id] = []
    return chat_sessions[session_id]


@app.route('/')
def index():
    """Main page."""
    try:
        # Get agent capabilities for the UI
        if agent:
            capabilities = agent.get_capabilities()
            status = capabilities.get('status', {})
            is_ready = all([
                status.get('llm_ready', False),
                status.get('recraft_ready', False),
                status.get('vector_ready', False)
            ])
        else:
            is_ready = False
            capabilities = {}
        
        return render_template('index.html', 
                             capabilities=capabilities,
                             is_ready=is_ready)
    except Exception as e:
        logger.error(f"Error loading index page: {str(e)}")
        return render_template('error.html', error=str(e))


@app.route('/api/status')
def api_status():
    """API status endpoint."""
    try:
        if not agent:
            return jsonify({
                'status': 'error',
                'message': 'Agent not initialized',
                'ready': False
            })
        
        capabilities = agent.get_capabilities()
        status = capabilities.get('status', {})
        
        return jsonify({
            'status': 'success',
            'ready': all([
                status.get('llm_ready', False),
                status.get('recraft_ready', False),
                status.get('vector_ready', False)
            ]),
            'capabilities': capabilities
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'ready': False
        })


@app.route('/api/styles')
def api_styles():
    """Get available Recraft styles."""
    try:
        if not agent:
            return jsonify({'error': 'Agent not initialized'}), 500
        
        # Get styles from settings
        styles_data = {
            'styles': Settings.RECRAFT_STYLES,
            'substyles': Settings.RECRAFT_SUBSTYLES
        }
        
        return jsonify({
            'status': 'success',
            'data': styles_data
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/generate', methods=['POST'])
def api_generate():
    """Generate image via API."""
    try:
        if not agent:
            return jsonify({'error': 'Agent not initialized'}), 500
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        prompt = data.get('prompt', '')
        if not prompt:
            return jsonify({'error': 'No prompt provided'}), 400
        
        session_id = data.get('session_id', str(uuid.uuid4()))
        history = get_session_history(session_id)
        
        # Enhanced prompt based on options
        options = data.get('options', {})
        enhanced_prompt = prompt
        
        if options.get('vector_output'):
            enhanced_prompt = f"Generate vector graphics: {prompt}. Use generate_vector=True for scalable output."
        
        if options.get('style'):
            enhanced_prompt = f"{enhanced_prompt} Style: {options['style']}"
        
        if options.get('substyle'):
            enhanced_prompt = f"{enhanced_prompt} Substyle: {options['substyle']}"
        
        if options.get('size'):
            enhanced_prompt = f"{enhanced_prompt} Size: {options['size']}"
        
        # Generate response
        response = agent.invoke(enhanced_prompt, history)
        
        # Update session history
        from langchain_core.messages import HumanMessage, AIMessage
        history.append(HumanMessage(content=prompt))
        history.append(AIMessage(content=response))
        
        # Keep history manageable
        if len(history) > 20:
            chat_sessions[session_id] = history[-20:]
        
        return jsonify({
            'status': 'success',
            'response': response,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in generate API: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/process', methods=['POST'])
def api_process():
    """Process uploaded image."""
    try:
        if not agent:
            return jsonify({'error': 'Agent not initialized'}), 500
        
        # Handle file upload
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not allowed'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)
        
        # Get processing instructions
        operation = request.form.get('operation', 'enhance')
        instructions = request.form.get('instructions', '')
        session_id = request.form.get('session_id', str(uuid.uuid4()))
        
        history = get_session_history(session_id)
        
        # Create processing prompt
        prompt = f"Use RecraftProcessingTool to {operation} the uploaded image {unique_filename}. {instructions}"
        
        # Process with agent
        response = agent.invoke(prompt, history)
        
        # Update history
        from langchain_core.messages import HumanMessage, AIMessage
        history.append(HumanMessage(content=f"Process image: {operation} - {instructions}"))
        history.append(AIMessage(content=response))
        
        if len(history) > 20:
            chat_sessions[session_id] = history[-20:]
        
        return jsonify({
            'status': 'success',
            'response': response,
            'uploaded_file': unique_filename,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in process API: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/chat', methods=['POST'])
def api_chat():
    """Chat with the agent."""
    try:
        if not agent:
            return jsonify({'error': 'Agent not initialized'}), 500
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        message = data.get('message', '')
        if not message:
            return jsonify({'error': 'No message provided'}), 400
        
        session_id = data.get('session_id', str(uuid.uuid4()))
        history = get_session_history(session_id)
        
        # Process with agent
        response = agent.invoke(message, history)
        
        # Update history
        from langchain_core.messages import HumanMessage, AIMessage
        history.append(HumanMessage(content=message))
        history.append(AIMessage(content=response))
        
        if len(history) > 20:
            chat_sessions[session_id] = history[-20:]
        
        return jsonify({
            'status': 'success',
            'response': response,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in chat API: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/history/<session_id>')
def api_history(session_id):
    """Get chat history for a session."""
    try:
        history = get_session_history(session_id)
        
        # Convert to JSON-serializable format
        serialized_history = []
        for msg in history:
            msg_type = 'human' if hasattr(msg, 'content') and 'HumanMessage' in str(type(msg)) else 'ai'
            serialized_history.append({
                'type': msg_type,
                'content': msg.content,
                'timestamp': datetime.now().isoformat()  # Simplified for demo
            })
        
        return jsonify({
            'status': 'success',
            'history': serialized_history,
            'session_id': session_id
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)


@app.route('/generated_images/<filename>')
def generated_file(filename):
    """Serve generated images."""
    return send_from_directory(app.config['OUTPUT_FOLDER'], filename)


@app.route('/vector')
def vector_page():
    """Vector graphics generation page."""
    return render_template('vector.html')


@app.route('/processing')
def processing_page():
    """Image processing page."""
    return render_template('processing.html')


@app.route('/gallery')
def gallery_page():
    """Gallery of generated images."""
    try:
        # Get list of generated images
        images = []
        if os.path.exists(app.config['OUTPUT_FOLDER']):
            for filename in os.listdir(app.config['OUTPUT_FOLDER']):
                if allowed_file(filename):
                    filepath = os.path.join(app.config['OUTPUT_FOLDER'], filename)
                    stat = os.stat(filepath)
                    images.append({
                        'filename': filename,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'is_vector': filename.lower().endswith(('.svg', '.pdf'))
                    })
        
        # Sort by creation date (newest first)
        images.sort(key=lambda x: x['created'], reverse=True)
        
        return render_template('gallery.html', images=images)
        
    except Exception as e:
        logger.error(f"Error loading gallery: {str(e)}")
        return render_template('error.html', error=str(e))


@app.route('/health')
def health_check():
    """Health check endpoint."""
    try:
        status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'agent_initialized': agent is not None
        }
        
        if agent:
            capabilities = agent.get_capabilities()
            status['agent_status'] = capabilities.get('status', {})
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors."""
    return render_template('error.html', 
                         error="Page not found", 
                         error_code=404), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return render_template('error.html', 
                         error="Internal server error", 
                         error_code=500), 500


if __name__ == '__main__':
    print("🚀 Starting Recraft Image Generation Web App...")
    print("=" * 60)
    
    # Initialize agent
    if init_agent():
        print("✅ Recraft agent initialized successfully")
    else:
        print("❌ Failed to initialize agent - some features may not work")
    
    print(f"📁 Upload folder: {UPLOAD_FOLDER}")
    print(f"📁 Output folder: {OUTPUT_FOLDER}")
    print("=" * 60)
    
    # Run the app
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.getenv('PORT', 5000))
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode,
        threaded=True
    )