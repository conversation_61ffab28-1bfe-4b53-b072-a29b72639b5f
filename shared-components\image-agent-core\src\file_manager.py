"""
Shared file management utilities for all Image Agent providers.
Handles file operations, directory management, and storage utilities.
"""
import os
import shutil
import json
import tempfile
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import hashlib
import logging

logger = logging.getLogger(__name__)


class FileManager:
    """Handles file system operations for Image Agents."""
    
    def __init__(self, base_path: str = None):
        """
        Initialize FileManager with a base path.
        
        Args:
            base_path: Base directory for file operations
        """
        self.base_path = Path(base_path) if base_path else Path.cwd()
        self.ensure_directory(self.base_path)
        
    def ensure_directory(self, path: Union[str, Path]) -> Path:
        """
        Ensure directory exists, create if it doesn't.
        
        Args:
            path: Directory path to ensure
            
        Returns:
            Path object of the directory
        """
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Ensured directory exists: {path}")
        return path
    
    def get_unique_filename(self, directory: Union[str, Path], base_name: str, extension: str = None) -> str:
        """
        Generate a unique filename in the given directory.
        
        Args:
            directory: Target directory
            base_name: Base name for the file
            extension: File extension (with or without dot)
            
        Returns:
            Unique filename
        """
        directory = Path(directory)
        self.ensure_directory(directory)
        
        # Clean extension
        if extension and not extension.startswith('.'):
            extension = f'.{extension}'
        elif not extension:
            extension = ''
        
        # Try base name first
        candidate = f"{base_name}{extension}"
        full_path = directory / candidate
        
        if not full_path.exists():
            return candidate
        
        # Add timestamp and counter if needed
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        candidate = f"{base_name}_{timestamp}{extension}"
        full_path = directory / candidate
        
        if not full_path.exists():
            return candidate
        
        # Add counter if timestamp isn't enough
        counter = 1
        while full_path.exists():
            candidate = f"{base_name}_{timestamp}_{counter}{extension}"
            full_path = directory / candidate
            counter += 1
        
        logger.debug(f"Generated unique filename: {candidate}")
        return candidate
    
    def save_file(self, content: bytes, directory: Union[str, Path], filename: str) -> str:
        """
        Save binary content to a file.
        
        Args:
            content: Binary content to save
            directory: Target directory
            filename: Target filename
            
        Returns:
            Full path to saved file
        """
        directory = Path(directory)
        self.ensure_directory(directory)
        
        file_path = directory / filename
        with open(file_path, 'wb') as f:
            f.write(content)
        
        logger.debug(f"Saved file: {file_path} ({len(content)} bytes)")
        return str(file_path)
    
    def save_text_file(self, content: str, directory: Union[str, Path], filename: str, encoding: str = 'utf-8') -> str:
        """
        Save text content to a file.
        
        Args:
            content: Text content to save
            directory: Target directory
            filename: Target filename
            encoding: Text encoding
            
        Returns:
            Full path to saved file
        """
        directory = Path(directory)
        self.ensure_directory(directory)
        
        file_path = directory / filename
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        
        logger.debug(f"Saved text file: {file_path} ({len(content)} characters)")
        return str(file_path)
    
    def read_file(self, file_path: Union[str, Path]) -> bytes:
        """
        Read binary content from a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Binary content of the file
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with open(file_path, 'rb') as f:
            content = f.read()
        
        logger.debug(f"Read file: {file_path} ({len(content)} bytes)")
        return content
    
    def read_text_file(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """
        Read text content from a file.
        
        Args:
            file_path: Path to the file
            encoding: Text encoding
            
        Returns:
            Text content of the file
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
        
        logger.debug(f"Read text file: {file_path} ({len(content)} characters)")
        return content
    
    def delete_file(self, file_path: Union[str, Path]) -> bool:
        """
        Delete a file safely.
        
        Args:
            file_path: Path to the file to delete
            
        Returns:
            True if file was deleted, False if it didn't exist
        """
        file_path = Path(file_path)
        if file_path.exists():
            file_path.unlink()
            logger.debug(f"Deleted file: {file_path}")
            return True
        else:
            logger.debug(f"File not found for deletion: {file_path}")
            return False
    
    def copy_file(self, source: Union[str, Path], destination: Union[str, Path]) -> str:
        """
        Copy a file from source to destination.
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Returns:
            Path to copied file
        """
        source = Path(source)
        destination = Path(destination)
        
        if not source.exists():
            raise FileNotFoundError(f"Source file not found: {source}")
        
        # Ensure destination directory exists
        self.ensure_directory(destination.parent)
        
        shutil.copy2(source, destination)
        logger.debug(f"Copied file from {source} to {destination}")
        return str(destination)
    
    def move_file(self, source: Union[str, Path], destination: Union[str, Path]) -> str:
        """
        Move a file from source to destination.
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Returns:
            Path to moved file
        """
        source = Path(source)
        destination = Path(destination)
        
        if not source.exists():
            raise FileNotFoundError(f"Source file not found: {source}")
        
        # Ensure destination directory exists
        self.ensure_directory(destination.parent)
        
        shutil.move(str(source), str(destination))
        logger.debug(f"Moved file from {source} to {destination}")
        return str(destination)
    
    def list_files(self, directory: Union[str, Path], pattern: str = '*', recursive: bool = False) -> List[str]:
        """
        List files in a directory matching a pattern.
        
        Args:
            directory: Directory to search
            pattern: Glob pattern to match
            recursive: Whether to search recursively
            
        Returns:
            List of file paths
        """
        directory = Path(directory)
        if not directory.exists():
            return []
        
        if recursive:
            files = list(directory.rglob(pattern))
        else:
            files = list(directory.glob(pattern))
        
        # Filter to only files (not directories)
        file_paths = [str(f) for f in files if f.is_file()]
        logger.debug(f"Listed {len(file_paths)} files in {directory} with pattern '{pattern}'")
        return file_paths
    
    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        stat = file_path.stat()
        info = {
            'name': file_path.name,
            'path': str(file_path.absolute()),
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'accessed': datetime.fromtimestamp(stat.st_atime),
            'extension': file_path.suffix,
            'is_file': file_path.is_file(),
            'is_directory': file_path.is_dir()
        }
        
        logger.debug(f"Got file info for {file_path}")
        return info
    
    def calculate_file_hash(self, file_path: Union[str, Path], algorithm: str = 'sha256') -> str:
        """
        Calculate hash of a file.
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm ('md5', 'sha1', 'sha256')
            
        Returns:
            Hex digest of the file hash
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        hash_obj = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        file_hash = hash_obj.hexdigest()
        logger.debug(f"Calculated {algorithm} hash for {file_path}: {file_hash}")
        return file_hash
    
    def clean_directory(self, directory: Union[str, Path], older_than_days: int = 7, pattern: str = '*') -> int:
        """
        Clean old files from a directory.
        
        Args:
            directory: Directory to clean
            older_than_days: Remove files older than this many days
            pattern: Glob pattern for files to consider
            
        Returns:
            Number of files deleted
        """
        directory = Path(directory)
        if not directory.exists():
            return 0
        
        cutoff_time = datetime.now().timestamp() - (older_than_days * 24 * 60 * 60)
        deleted_count = 0
        
        for file_path in directory.glob(pattern):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    deleted_count += 1
                    logger.debug(f"Deleted old file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to delete {file_path}: {e}")
        
        logger.info(f"Cleaned {deleted_count} files from {directory}")
        return deleted_count


class ConfigManager:
    """Handles configuration file management."""
    
    def __init__(self, config_path: Union[str, Path] = None):
        """
        Initialize ConfigManager.
        
        Args:
            config_path: Path to configuration file or directory
        """
        if config_path:
            self.config_path = Path(config_path)
            if self.config_path.is_dir():
                self.config_path = self.config_path / 'config.json'
        else:
            self.config_path = Path.cwd() / 'config.json'
    
    def save_config(self, config: Dict[str, Any]) -> str:
        """
        Save configuration to file.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Path to saved config file
        """
        # Ensure directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, default=str)
        
        logger.debug(f"Saved configuration to {self.config_path}")
        return str(self.config_path)
    
    def load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file.
        
        Returns:
            Configuration dictionary
        """
        if not self.config_path.exists():
            logger.debug(f"Config file not found: {self.config_path}")
            return {}
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            logger.debug(f"Loaded configuration from {self.config_path}")
            return config
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {self.config_path}: {e}")
            return {}
    
    def update_config(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of updates to apply
            
        Returns:
            Updated configuration dictionary
        """
        config = self.load_config()
        config.update(updates)
        self.save_config(config)
        return config
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get a specific configuration value.
        
        Args:
            key: Configuration key (supports dot notation for nested)
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        config = self.load_config()
        
        # Support dot notation for nested keys
        keys = key.split('.')
        value = config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value


class TempFileManager:
    """Manages temporary files and cleanup."""
    
    def __init__(self, prefix: str = 'imageagent_'):
        """
        Initialize TempFileManager.
        
        Args:
            prefix: Prefix for temporary files
        """
        self.prefix = prefix
        self.temp_files = []
    
    def create_temp_file(self, suffix: str = '', content: bytes = None) -> str:
        """
        Create a temporary file.
        
        Args:
            suffix: File suffix/extension
            content: Content to write to the file
            
        Returns:
            Path to temporary file
        """
        fd, temp_path = tempfile.mkstemp(prefix=self.prefix, suffix=suffix)
        
        if content:
            with os.fdopen(fd, 'wb') as f:
                f.write(content)
        else:
            os.close(fd)
        
        self.temp_files.append(temp_path)
        logger.debug(f"Created temporary file: {temp_path}")
        return temp_path
    
    def create_temp_dir(self) -> str:
        """
        Create a temporary directory.
        
        Returns:
            Path to temporary directory
        """
        temp_dir = tempfile.mkdtemp(prefix=self.prefix)
        self.temp_files.append(temp_dir)
        logger.debug(f"Created temporary directory: {temp_dir}")
        return temp_dir
    
    def cleanup(self):
        """Clean up all temporary files and directories."""
        for temp_path in self.temp_files:
            try:
                path = Path(temp_path)
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path)
                logger.debug(f"Cleaned up temporary path: {temp_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup {temp_path}: {e}")
        
        self.temp_files.clear()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()