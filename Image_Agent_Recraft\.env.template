# Recraft API Configuration
RECRAFT_API_KEY=your_recraft_api_key_here

# Model Configuration
LANGUAGE_MODEL=gpt-4o-mini
RECRAFT_MODEL=recraftv3

# Default Image Generation Parameters
DEFAULT_IMAGE_SIZE=1024x1024
DEFAULT_IMAGE_QUALITY=high
DEFAULT_OUTPUT_FORMAT=png
DEFAULT_STYLE=any
DEFAULT_SUBSTYLE=

# Vector Graphics Configuration
VECTOR_GENERATION_ENABLED=true
VECTOR_OUTPUT_FORMATS=svg,pdf
DEFAULT_VECTOR_FORMAT=svg

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO

# File Storage Configuration
GENERATED_IMAGES_DIR=generated_images

# Recraft API Configuration
RECRAFT_API_URL=https://external.api.recraft.ai/v1
RECRAFT_CLIENT_TYPE=Image_Agent_Recraft

# Image Processing Configuration
MAX_IMAGE_SIZE=50MB
SUPPORTED_FORMATS=png,jpg,jpeg,svg,pdf,webp
IMAGE_COMPRESSION=85

# Web Interface Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_SECRET_KEY=your_secret_key_here

# Vector Graphics Processing
CAIRO_BACKEND=true
SVG_OPTIMIZATION=true
PDF_QUALITY=300